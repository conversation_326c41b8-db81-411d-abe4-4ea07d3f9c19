/**
 * 统一的模型管理器
 * 直接使用 AIModelConfig 接口，避免不必要的转换
 */

import { ALL_GRSAI_MODELS } from './grsai/models';
import { ALL_REPLICATE_MODELS } from './replicate/models';
import { AIModelConfig } from './types/model';

/**
 * 获取所有模型（包括 GRSAI 和 Replicate）
 */
function getAllModels(): AIModelConfig[] {
  // 直接合并所有模型，不需要转换
  return [
    ...ALL_GRSAI_MODELS,
    ...ALL_REPLICATE_MODELS
  ];
}

/**
 * 获取所有活跃的AI模型
 */
export function getActiveAIModels(): AIModelConfig[] {
  return getAllModels()
    .filter(model => model.is_active)
    .sort((a, b) => {
      // 先按模型类型排序，再按积分消耗排序
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}

/**
 * 根据模型类型获取模型列表
 */
export function getAIModelsByType(type: string): AIModelConfig[] {
  return getAllModels()
    .filter(model => model.is_active && model.model_type === type)
    .sort((a, b) => a.credits_per_unit - b.credits_per_unit);
}

/**
 * 根据模型ID获取单个模型
 */
export function getAIModelById(modelId: string): AIModelConfig | null {
  const model = getAllModels().find(m => m.model_id === modelId && m.is_active);
  return model || null;
}

/**
 * 根据模型ID获取单个模型（包括不活跃的模型）
 */
export function getAIModelByIdIncludeInactive(modelId: string): AIModelConfig | null {
  const model = getAllModels().find(m => m.model_id === modelId);
  return model || null;
}

/**
 * 根据提供商获取模型列表
 */
export function getAIModelsByProvider(provider: string): AIModelConfig[] {
  return getAllModels()
    .filter(model => model.is_active && model.provider === provider)
    .sort((a, b) => {
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}

// 为了向后兼容，导出带有 Local 前缀的函数
export const getLocalActiveAIModels = getActiveAIModels;
export const getLocalAIModelsByType = getAIModelsByType;
export const getLocalAIModelById = getAIModelById;
export const getLocalAIModelByIdIncludeInactive = getAIModelByIdIncludeInactive;
export const getLocalAIModelsByProvider = getAIModelsByProvider;

// 导出类型别名以保持兼容性
export type LocalAIModel = AIModelConfig;
export type UnifiedModel = AIModelConfig; // 向后兼容