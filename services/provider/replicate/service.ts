import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { AIRequest, AIResponse } from "@/types/ai-model";
import {

  createAIModelUsage,
  updateAIModelUsageByTaskId
} from "@/models/ai-model";
import {
getAIModelById}
from "@/services/provider/model-manager";
import { ReplicateProvider } from "./api";
import type { ReplicateImageRequest } from "./types";
import { WatermarkService } from "../../watermark";



/**
 * 独立的 Replicate AI 服务
 * 完全独立于 GRSAI provider，实现相同的接口和生命周期管理
 */
export class ReplicateAIService {
  private replicateProvider: ReplicateProvider;

  constructor() {
    this.replicateProvider = new ReplicateProvider();
  }

  /**
   * 处理AI请求 - 与 GrsAIService 相同的接口
   */
  async processRequestFromClient(
    userUuid: string,
    request: AIRequest
  ): Promise<AIResponse> {
    const taskId = getUuid();
    
    console.log(`[Replicate AI Service] Processing request for user: ${userUuid}, model: ${request.model}`);
    
    // 获取模型配置
    const model = await getAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    // 验证是否为 Replicate 模型
    if (model.provider !== 'replicate') {
      throw new Error(`Model ${request.model} is not a Replicate model`);
    }

    // 估算成本
    const estimatedCost = await this.estimateCost(request, model);
    
    console.log(`[Replicate AI Service] Estimated cost: ${estimatedCost} credits`);
    
    // 创建使用记录
    await createAIModelUsage({
      user_uuid: userUuid,
      model_id: request.model,
      task_id: taskId,
      provider: 'replicate',
      credits_consumed: estimatedCost,
      status: 'pending',
      request_params: request,
      started_at: getIsoTimestr()
    });

    console.log(`[Replicate AI Service] Created usage record: ${taskId}`);

    try {
      let response: AIResponse;

      switch (request.type) {
        case 'image':
          response = await this.handleImageGeneration(request, taskId);
          break;
        default:
          throw new Error(`Unsupported request type: ${request.type} for Replicate provider`);
      }

      // 更新使用记录
      if (response.status === 'success') {
        await updateAIModelUsageByTaskId(taskId, {
          status: 'success',
          output_size: this.calculateOutputSize(response),
          response_data: response,
          completed_at: getIsoTimestr()
        });
        
        console.log(`[Replicate AI Service] Updated usage record to success`);
      }

      return response;
    } catch (error) {
      console.error(`[Replicate AI Service] Request failed:`, error);
      
      // 更新失败记录
      await updateAIModelUsageByTaskId(taskId, {
        status: 'failed',
        error_reason: 'error',
        error_detail: error instanceof Error ? error.message : 'Unknown error',
        completed_at: getIsoTimestr()
      });

      throw error;
    }
  }

  /**
   * 处理图像生成
   */
  private async handleImageGeneration(request: AIRequest, taskId: string): Promise<AIResponse> {
    console.log(`[Replicate AI Service] Starting image generation for model: ${request.model}`);

    const model = await getAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    // 构建 Replicate 请求
    const replicateRequest: ReplicateImageRequest = {
      model: request.model,
      prompt: request.prompt,
      options: {
        // 基础参数映射
        aspect_ratio: request.options?.aspectRatio,
        num_outputs: request.options?.variants || 1,
        output_format: request.options?.output_format || 'webp',
        output_quality: request.options?.output_quality || 90,
        
        // 高级参数
        guidance: request.options?.guidance,
        num_inference_steps: request.options?.num_inference_steps,
        prompt_strength: request.options?.prompt_strength,
        seed: request.options?.seed,
        disable_safety_checker: request.options?.disable_safety_checker,
        go_fast: request.options?.go_fast,
        megapixels: request.options?.megapixels,
        
        // img2img 支持
        image: request.options?.uploadedImages?.[0]
      }
    };

    console.log(`[Replicate AI Service] Calling Replicate API with request:`, replicateRequest);

    // 调用 Replicate API（异步模式）
    const replicateResponse = await this.replicateProvider.generateImage(replicateRequest);

    console.log(`[Replicate AI Service] Replicate response:`, replicateResponse);

    if (replicateResponse.status === 'failed') {
      throw new Error(replicateResponse.error || 'Replicate API failed');
    }

    // 更新使用记录，保存Replicate预测ID
    await updateAIModelUsageByTaskId(taskId, {
      external_request_id: replicateResponse.id,
      response_data: replicateResponse,
      status: 'pending'
    });

    // 对于异步任务，返回pending状态
    if (replicateResponse.status === 'starting' || replicateResponse.status === 'processing') {
      return {
        id: taskId,
        task_id: taskId,
        type: 'image',
        status: 'pending',
        progress: replicateResponse.progress || 0,
        usage: {
          credits_consumed: model.credits_per_unit * (request.options?.variants || 1)
        }
      };
    }

    // 如果已经完成，处理结果
    if (replicateResponse.status === 'succeeded' && replicateResponse.urls) {
      // 添加水印并转存文件到我们的存储
      console.log(`[Replicate AI Service] Processing ${replicateResponse.urls.length} files with watermark and transferring to our storage`);

      const transferResults = await this.processImagesWithWatermark(
        replicateResponse.urls,
        taskId
      );

      // 检查转存结果
      const successfulTransfers = transferResults.filter(r => r.success);
      const failedTransfers = transferResults.filter(r => !r.success);

      if (failedTransfers.length > 0) {
        console.warn(`[Replicate AI Service] Some file transfers failed:`, failedTransfers);
      }

      if (successfulTransfers.length === 0) {
        throw new Error('All file transfers failed');
      }

      // 构建最终响应
      const images = successfulTransfers.map(transfer => ({
        url: transfer.url!,
        width: 1024, // Replicate 默认尺寸，可以根据实际情况调整
        height: 1024
      }));

      console.log(`[Replicate AI Service] Successfully transferred ${images.length} images`);

      return {
        id: taskId,
        task_id: taskId,
        type: 'image',
        status: 'success',
        result: {
          images
        },
        usage: {
          credits_consumed: model.credits_per_unit * (request.options?.variants || 1)
        }
      };
    }

    // 如果到这里，说明是其他状态，视为失败
    return {
      id: taskId,
      task_id: taskId,
      type: 'image',
      status: 'failed',
      error: {
        reason: 'error',
        detail: 'Unexpected response from Replicate API'
      },
      usage: {
        credits_consumed: model.credits_per_unit * (request.options?.variants || 1)
      }
    };
  }

  /**
   * 估算请求成本
   */
  private async estimateCost(request: AIRequest, model: any): Promise<number> {
    switch (request.type) {
      case 'image':
        const variants = request.options?.variants || 1;
        return model.credits_per_unit * variants;
      default:
        return model.credits_per_unit;
    }
  }

  /**
   * 计算输出大小
   */
  private calculateOutputSize(response: AIResponse): number {
    if (response.result?.images) {
      return response.result.images.length;
    }
    return 0;
  }

  /**
   * 检查模型是否支持
   */
  isModelSupported(modelId: string): boolean {
    return this.replicateProvider.isModelSupported(modelId);
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): string[] {
    return this.replicateProvider.getSupportedModels();
  }

  /**
   * 查询Replicate预测结果
   */
  async getReplicateResult(predictionId: string): Promise<any> {
    try {
      console.log(`[Replicate AI Service] Querying Replicate prediction: ${predictionId}`);

      const replicateResponse = await this.replicateProvider.getPrediction(predictionId);

      console.log(`[Replicate AI Service] Replicate prediction response:`, replicateResponse);

      return replicateResponse;
    } catch (error) {
      console.error(`[Replicate AI Service] Error querying Replicate prediction:`, error);
      throw error;
    }
  }

  /**
   * 处理图片添加水印并转存
   */
  private async processImagesWithWatermark(
    urls: string[],
    baseTaskId: string
  ): Promise<Array<{
    originalUrl: string;
    success: boolean;
    url?: string;
    key?: string;
    error?: string;
  }>> {
    const results = [];
    const watermarkConfig = WatermarkService.getDefaultConfig();

    console.log(`[Replicate AI Service] Processing ${urls.length} images with watermark config:`, watermarkConfig);

    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      const taskId = urls.length > 1 ? `${baseTaskId}_${i}` : baseTaskId;

      try {
        console.log(`[Replicate AI Service] Processing image ${i + 1}/${urls.length}: ${url}`);

        // 下载图片并添加水印
        const watermarkedBuffer = await WatermarkService.addWatermarkFromUrl(url, watermarkConfig);

        console.log(`[Replicate AI Service] Watermark added successfully, buffer size: ${watermarkedBuffer.length} bytes`);

        // 上传到我们的存储
        const uploadResult = await this.uploadWatermarkedImage(watermarkedBuffer, taskId);

        results.push({
          originalUrl: url,
          success: true,
          url: uploadResult.url,
          key: uploadResult.key
        });

        console.log(`[Replicate AI Service] Image ${i + 1} processed successfully: ${uploadResult.url}`);
      } catch (error) {
        console.error(`[Replicate AI Service] Error processing image ${i + 1}:`, error);
        results.push({
          originalUrl: url,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * 上传带水印的图片到存储
   */
  private async uploadWatermarkedImage(
    imageBuffer: Buffer,
    taskId: string
  ): Promise<{ url: string; key: string }> {
    try {
      const { newStorage } = await import("@/lib/storage");

      // 构建存储路径
      const bucket = process.env.STORAGE_BUCKET || 'shipany-test';
      const key = `ai-generated/image/${taskId}.png`;

      console.log(`[Replicate AI Service] Uploading watermarked image to bucket: ${bucket}, key: ${key}`);

      // 使用存储服务上传
      const storage = newStorage();
      const result = await storage.uploadFile({
        body: imageBuffer,
        key: key,
        bucket: bucket,
        contentType: 'image/png',
        disposition: 'inline'
      });

      console.log(`[Replicate AI Service] Upload successful:`, result);

      return {
        url: result.url || '',
        key: result.key || ''
      };
    } catch (error) {
      console.error(`[Replicate AI Service] Upload failed:`, error);
      throw new Error(`Failed to upload watermarked image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
