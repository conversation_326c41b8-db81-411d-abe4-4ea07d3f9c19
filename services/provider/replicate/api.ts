import Replicate from "replicate";
import { getUuid } from "@/lib/hash";
import type { ReplicateImageRequest, ReplicateImageResponse } from './types';

/**
 * Replicate 提供商配置
 */
export const REPLICATE_CONFIG = {
  timeout: 60000,
  retryAttempts: 3,
  retryDelay: 1000,
  baseUrl: 'https://api.replicate.com',
  version: 'v1'
} as const;

export const REPLICATE_ENDPOINTS = {
  predictions: '/v1/predictions',
  models: '/v1/models'
} as const;

export const REPLICATE_STATUS = {
  STARTING: 'starting',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  CANCELED: 'canceled'
} as const;

/**
 * Replicate 提供商实现
 */
export class ReplicateProvider {
  private client: Replicate;

  constructor() {
    const apiToken = process.env.REPLICATE_API_TOKEN;
    if (!apiToken) {
      throw new Error('REPLICATE_API_TOKEN environment variable is required');
    }

    this.client = new Replicate({
      auth: apiToken,
    });
  }

  /**
   * 生成图像（异步模式）
   */
  async generateImage(request: ReplicateImageRequest): Promise<ReplicateImageResponse> {
    const requestId = getUuid();

    try {
      console.log(`[Replicate Provider] ========== STARTING ASYNC IMAGE GENERATION ==========`);
      console.log(`[Replicate Provider] Request ID: ${requestId}`);
      console.log(`[Replicate Provider] Full request:`, JSON.stringify(request, null, 2));

      // 构建输入参数
      const input: any = {
        prompt: request.prompt,
      };

      // 添加可选参数（按照官方文档）
      if (request.options?.aspect_ratio) {
        input.aspect_ratio = request.options.aspect_ratio;
      }
      if (request.options?.image) {
        input.image = request.options.image;
      }
      if (request.options?.prompt_strength !== undefined) {
        input.prompt_strength = typeof request.options.prompt_strength === 'string'
          ? parseFloat(request.options.prompt_strength)
          : request.options.prompt_strength;
      }
      if (request.options?.num_outputs !== undefined) {
        input.num_outputs = typeof request.options.num_outputs === 'string'
          ? parseInt(request.options.num_outputs)
          : request.options.num_outputs;
      }
      if (request.options?.num_inference_steps !== undefined) {
        input.num_inference_steps = typeof request.options.num_inference_steps === 'string'
          ? parseInt(request.options.num_inference_steps)
          : request.options.num_inference_steps;
      }
      if (request.options?.guidance !== undefined) {
        input.guidance = typeof request.options.guidance === 'string'
          ? parseFloat(request.options.guidance)
          : request.options.guidance;
      }
      if (request.options?.seed !== undefined) {
        input.seed = typeof request.options.seed === 'string'
          ? parseInt(request.options.seed)
          : request.options.seed;
      }
      if (request.options?.output_format) {
        input.output_format = request.options.output_format;
      }
      if (request.options?.output_quality !== undefined) {
        input.output_quality = typeof request.options.output_quality === 'string'
          ? parseInt(request.options.output_quality)
          : request.options.output_quality;
      }
      if (request.options?.disable_safety_checker !== undefined) {
        input.disable_safety_checker = typeof request.options.disable_safety_checker === 'string'
          ? request.options.disable_safety_checker === 'true'
          : request.options.disable_safety_checker;
      }
      if (request.options?.go_fast !== undefined) {
        input.go_fast = typeof request.options.go_fast === 'string'
          ? request.options.go_fast === 'true'
          : request.options.go_fast;
      }
      if (request.options?.megapixels) {
        input.megapixels = request.options.megapixels;
      }

      console.log(`[Replicate Provider] ========== CALLING REPLICATE ASYNC API ==========`);
      console.log(`[Replicate Provider] Model: ${request.model}`);
      console.log(`[Replicate Provider] Input parameters:`, JSON.stringify(input, null, 2));

      // 使用异步模式创建预测
      const prediction = await this.client.predictions.create({
        version: request.model,
        input: input
      });

      console.log(`[Replicate Provider] ========== REPLICATE PREDICTION CREATED ==========`);
      console.log(`[Replicate Provider] Prediction ID: ${prediction.id}`);
      console.log(`[Replicate Provider] Prediction status: ${prediction.status}`);
      console.log(`[Replicate Provider] Full prediction:`, JSON.stringify(prediction, null, 2));

      const result = {
        id: prediction.id, // 使用Replicate的预测ID
        status: this.mapReplicateStatus(prediction.status),
        progress: this.extractProgress(prediction),
        logs: prediction.logs || '',
        usage: {
          credits_consumed: this.calculateCredits(request.model)
        }
      };

      console.log(`[Replicate Provider] ========== FINAL ASYNC RESULT ==========`);
      console.log(`[Replicate Provider] Result:`, JSON.stringify(result, null, 2));
      return result;

    } catch (error) {
      console.log(`[Replicate Provider] ========== ERROR OCCURRED ==========`);
      console.error(`[Replicate Provider] Error type:`, typeof error);
      console.error(`[Replicate Provider] Error message:`, error instanceof Error ? error.message : 'Unknown error');
      console.error(`[Replicate Provider] Full error:`, error);
      console.error(`[Replicate Provider] Error stack:`, error instanceof Error ? error.stack : 'No stack');

      const errorResult = {
        id: requestId,
        status: 'failed' as const,
        error: error instanceof Error ? error.message : 'Unknown error',
        usage: {
          credits_consumed: 0
        }
      };

      console.log(`[Replicate Provider] ========== FINAL ERROR RESULT ==========`);
      console.log(`[Replicate Provider] Error result:`, JSON.stringify(errorResult, null, 2));
      return errorResult;
    }
  }

  /**
   * 查询预测结果
   */
  async getPrediction(predictionId: string): Promise<ReplicateImageResponse> {
    try {
      console.log(`[Replicate Provider] ========== GETTING PREDICTION ==========`);
      console.log(`[Replicate Provider] Prediction ID: ${predictionId}`);

      const prediction = await this.client.predictions.get(predictionId);

      console.log(`[Replicate Provider] ========== PREDICTION RESPONSE ==========`);
      console.log(`[Replicate Provider] Status: ${prediction.status}`);
      console.log(`[Replicate Provider] Full prediction:`, JSON.stringify(prediction, null, 2));

      // 处理输出
      let urls: string[] = [];
      if (prediction.status === 'succeeded' && prediction.output) {
        urls = this.extractUrlsFromOutput(prediction.output);
      }

      const result = {
        id: prediction.id,
        status: this.mapReplicateStatus(prediction.status),
        progress: this.extractProgress(prediction),
        urls: urls.length > 0 ? urls : undefined,
        output: prediction.output,
        logs: prediction.logs || '',
        error: prediction.error ? String(prediction.error) : undefined,
        usage: {
          credits_consumed: this.calculateCredits('')
        }
      };

      console.log(`[Replicate Provider] ========== FINAL PREDICTION RESULT ==========`);
      console.log(`[Replicate Provider] Result:`, JSON.stringify(result, null, 2));
      return result;
    } catch (error) {
      console.error(`[Replicate Provider] Error getting prediction:`, error);
      throw error;
    }
  }

  /**
   * 映射Replicate状态到我们的状态
   */
  private mapReplicateStatus(status: string): 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled' {
    switch (status) {
      case 'starting':
        return 'starting';
      case 'processing':
        return 'processing';
      case 'succeeded':
        return 'succeeded';
      case 'failed':
        return 'failed';
      case 'canceled':
        return 'canceled';
      default:
        return 'starting';
    }
  }

  /**
   * 从预测中提取进度信息
   */
  private extractProgress(prediction: any): number | undefined {
    // Replicate的进度信息可能在logs中，或者通过其他方式提供
    // 这里我们可以尝试解析logs来获取进度
    if (prediction.logs) {
      const progressMatch = prediction.logs.match(/(\d+)%/);
      if (progressMatch) {
        return parseInt(progressMatch[1]);
      }
    }

    // 根据状态返回估计进度
    switch (prediction.status) {
      case 'starting':
        return 0;
      case 'processing':
        return 50; // 估计值
      case 'succeeded':
        return 100;
      case 'failed':
      case 'canceled':
        return 0;
      default:
        return undefined;
    }
  }

  /**
   * 从输出中提取URL
   */
  private extractUrlsFromOutput(output: any): string[] {
    if (Array.isArray(output)) {
      return output.map((item) => {
        if (typeof item === 'string') {
          return item;
        } else if (item && typeof item === 'object') {
          if (typeof item.url === 'function') {
            return item.url().toString();
          } else if ('url' in item && typeof item.url === 'string') {
            return item.url;
          }
        }
        return null;
      }).filter(Boolean) as string[];
    }
    return [];
  }

  /**
   * 计算积分消耗
   */
  private calculateCredits(model: string): number {
    // 根据模型计算积分消耗
    switch (model) {
      case 'black-forest-labs/flux-krea-dev':
        return 25; // 每张图片 25 积分
      default:
        return 20; // 默认 20 积分
    }
  }

  /**
   * 检查模型是否支持
   */
  isModelSupported(model: string): boolean {
    const supportedModels = [
      'black-forest-labs/flux-krea-dev'
    ];
    return supportedModels.includes(model);
  }

  /**
   * 获取支持的模型列表
   */
  getSupportedModels(): string[] {
    return [
      'black-forest-labs/flux-krea-dev'
    ];
  }
}

/**
 * 导出单例实例
 */
export const replicateProvider = new ReplicateProvider();