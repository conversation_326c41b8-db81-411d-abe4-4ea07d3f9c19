/**
 * Replicate 模型配置导出
 * 合并模型配置和参数配置
 */

import { REPLICATE_IMAGE_MODELS } from './image-models';
import { AIModelConfig, ModelType, Provider, UnitType } from '../types/model';
import { ModelParameterConfig } from '../types';

/**
 * 所有 Replicate 模型配置
 */
export const ALL_REPLICATE_MODELS = [
  ...REPLICATE_IMAGE_MODELS
];

/**
 * 转换为参数管理器兼容的格式
 */
export const ALL_REPLICATE_PARAMETER_CONFIGS: ModelParameterConfig[] = ALL_REPLICATE_MODELS.map(model => ({
  modelId: model.model_id,
  version: '1.0',
  provider: model.provider,
  modelType: model.model_type,
  parameters: model.parameters,
  parameterGroups: model.parameter_groups
}));

/**
 * 根据模型ID获取模型配置
 */
export function getModelConfig(modelId: string) {
  return ALL_REPLICATE_MODELS.find(model => model.model_id === modelId);
}

/**
 * 根据模型类型获取模型配置
 */
export function getModelsByType(type: string) {
  return ALL_REPLICATE_MODELS.filter(model => model.model_type === type && model.is_active);
}

/**
 * 获取所有活跃的模型
 */
export function getAllActiveModels() {
  return ALL_REPLICATE_MODELS.filter(model => model.is_active);
}

/**
 * 根据提供商获取模型配置
 */
export function getModelsByProvider(provider: string) {
  return ALL_REPLICATE_MODELS.filter(model => 
    model.provider.toLowerCase() === provider.toLowerCase() && model.is_active
  );
}

/**
 * 获取模型支持的功能
 */
export function getModelSupportedFeatures(modelId: string): string[] {
  const model = getModelConfig(modelId);
  return model?.supported_features || [];
}

/**
 * 检查模型是否支持特定功能
 */
export function isModelFeatureSupported(modelId: string, feature: string): boolean {
  const supportedFeatures = getModelSupportedFeatures(modelId);
  return supportedFeatures.includes(feature);
}

/**
 * 获取模型的成本信息
 */
export function getModelCostInfo(modelId: string) {
  const model = getModelConfig(modelId);
  if (!model) return null;
  
  return {
    creditsPerUnit: model.credits_per_unit,
    unitType: model.unit_type,
    provider: model.provider
  };
}

/**
 * 获取模型的基础信息
 */
export function getModelBasicInfo(modelId: string) {
  const model = getModelConfig(modelId);
  if (!model) return null;
  
  return {
    id: model.model_id,
    name: model.model_name,
    type: model.model_type,
    provider: model.provider,
    description: model.description,
    icon: model.icon,
    translationKey: model.translationKey
  };
}
