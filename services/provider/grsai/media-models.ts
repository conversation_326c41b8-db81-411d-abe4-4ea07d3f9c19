/**
 * GRSAI 媒体模型配置（图像和视频）
 */

import {
  ASPECT_RATIO_OPTIONS,
  CDN_OPTIONS,
  SIZE_OPTIONS,
  DURATION_OPTIONS,
  RESOLUTION_OPTIONS,
  FPS_OPTIONS,
  QUALITY_OPTIONS
} from '../types';
import { AIModelConfig } from '../types/model';

/**
 * GRSAI 媒体模型配置
 */
export const GRSAI_MEDIA_MODELS: AIModelConfig[] = [
  // 图像生成模型
  {
    model_id: 'flux-pro-1.1',
    model_model_name: '',
    translationKey: 'grsai.flux-pro-1.1',
    model_type: 'image',
    provider: 'grsai',
    api_endpoint: '/v1/draw/flux',
    credits_per_unit: 30,
    unit_type: 'images',
    is_active: true,
    description: '',
    max_input_size: 4000,
    supported_features: ['image_generation', 'professional', 'flux_tech'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    model_id: 'gpt-4o-image',
    model_name: '',
    translationKey: 'grsai.gpt-4o-image',
    model_type: 'image',
    provider: 'grsai',
    api_endpoint: '/v1/draw/completions',
    credits_per_unit: 40,
    unit_type: 'images',
    is_active: true,
    description: '',
    max_input_size: 4000,
    supported_features: ['image_generation', 'gpt_powered', 'versatile'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: SIZE_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    model_id: 'flux-kontext-pro',
    model_name: '',
    translationKey: 'grsai.flux-kontext-pro',
    model_type: 'image',
    provider: 'grsai',
    api_endpoint: '/v1/draw/flux',
    credits_per_unit: 45,
    unit_type: 'images',
    is_active: true,
    description: '',
    max_input_size: 4000,
    supported_features: ['image_generation', 'context_aware', 'professional'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    model_id: 'sora-image',
    model_name: '',
    translationKey: 'grsai.sora-image',
    model_type: 'image',
    provider: 'grsai',
    api_endpoint: '/v1/draw/completions',
    credits_per_unit: 50,
    unit_type: 'images',
    is_active: true,
    description: '',
    max_input_size: 4000,
    supported_features: ['image_generation', 'high_quality', 'creative'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: SIZE_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    model_id: 'flux-pro-1.1-ultra',
    model_name: '',
    translationKey: 'grsai.flux-pro-1.1-ultra',
    model_type: 'image',
    provider: 'grsai',
    api_endpoint: '/v1/draw/flux',
    credits_per_unit: 60,
    unit_type: 'images',
    is_active: true,
    description: '',
    max_input_size: 4000,
    supported_features: ['image_generation', 'ultra_quality', 'enhanced_flux'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    model_id: 'flux-kontext-max',
    model_name: '',
    translationKey: 'grsai.flux-kontext-max',
    model_type: 'image',
    provider: 'grsai',
    api_endpoint: '/v1/draw/flux',
    credits_per_unit: 80,
    unit_type: 'images',
    is_active: true,
    description: '',
    max_input_size: 4000,
    supported_features: ['image_generation', 'max_quality', 'context_aware'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  // 视频生成模型
  {
    model_id: 'veo3-fast',
    model_name: '',
    translationKey: 'grsai.veo3-fast',
    model_type: 'video',
    provider: 'grsai',
    api_endpoint: '/v1/video/veo',
    credits_per_unit: 100,
    unit_type: 'videos',
    is_active: true,
    description: '',
    max_input_size: 2000,
    supported_features: ['video_generation', 'fast', 'veo3_tech'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: DURATION_OPTIONS.slice(0, 2),
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '720p',
        options: RESOLUTION_OPTIONS.slice(0, 2),
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '24',
        options: FPS_OPTIONS.slice(0, 2),
        group: 'advanced'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'cdn'],
      expert: []
    }
  },

  {
    model_id: 'veo3-pro',
    model_name: '',
    translationKey: 'grsai.veo3-pro',
    model_type: 'video',
    provider: 'grsai',
    api_endpoint: '/v1/video/veo',
    credits_per_unit: 200,
    unit_type: 'videos',
    is_active: true,
    description: '',
    max_input_size: 2000,
    supported_features: ['video_generation', 'professional', 'advanced_veo3'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: DURATION_OPTIONS,
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '1080p',
        options: RESOLUTION_OPTIONS,
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '30',
        options: FPS_OPTIONS,
        group: 'advanced'
      },
      {
        name: 'quality',
        type: 'select',
        required: false,
        default: 'high',
        options: QUALITY_OPTIONS,
        group: 'advanced'
      },
      {
        name: 'motion_intensity',
        type: 'number',
        required: false,
        default: 5,
        min: 1,
        max: 10,
        group: 'expert'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'quality', 'cdn'],
      expert: ['motion_intensity']
    }
  }
];