import { getUuid } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { AIRequest, AIResponse } from "@/types/ai-model";
import {
  calculateModelCost,
  createAIModelUsage,
  updateAIModelUsageByTaskId,
} from "@/models/ai-model";
import { getAIModelById } from "@/services/provider/model-manager";
import { GRSAIProvider } from './api';
import type {
  GRSAITextRequest,
  GRSAIImageRequest,
  GRSAIFluxRequest,
  GRSAIVideoRequest,
  GRSAITextResponse,
  GRSAIUnifiedRequest
} from './api-types';

/**
 * GRSAI 服务接口
 */
export class GrsAIService {
  private grsaiProvider: GRSAIProvider;

  constructor() {
    this.grsaiProvider = new GRSAIProvider();
  }

  /**
   * 处理AI请求
   */
  async processRequestFromClient(
    userUuid: string,
    request: AIRequest
  ): Promise<AIResponse> {
    const taskId = getUuid(); // 统一任务ID

    // 获取模型配置
    const model = getAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    // 估算成本
    const estimatedCost = this.estimateCost(request, model);

    // 创建使用记录
    await createAIModelUsage({
      user_uuid: userUuid,
      model_id: request.model,
      task_id: taskId,
      provider: 'grsai',
      credits_consumed: estimatedCost,
      status: 'pending',
      request_params: request,
      started_at: getIsoTimestr()
    });

    try {
      let response: AIResponse;

      switch (request.type) {
        case 'text':
          response = await this.handleTextGeneration(request, taskId);
          break;
        case 'image':
          response = await this.handleImageGeneration(request, taskId);
          break;
        case 'video':
          response = await this.handleVideoGeneration(request, taskId);
          break;
        default:
          throw new Error(`Unsupported request type: ${request.type}`);
      }

      // 更新使用记录（只有在非pending状态时才更新为完成）
      if (response.status !== 'pending') {
        await updateAIModelUsageByTaskId(taskId, {
          status: response.status === 'success' ? 'success' : 'failed',
          output_size: this.calculateOutputSize(response),
          response_data: response,
          completed_at: getIsoTimestr()
        });
      }

      return response;
    } catch (error) {
      console.error(`[AI Service] Request failed:`, error);
      
      // 更新失败记录
      await updateAIModelUsageByTaskId(taskId, {
        status: 'failed',
        error_reason: 'error',
        error_detail: error instanceof Error ? error.message : 'Unknown error',
        completed_at: getIsoTimestr()
      });

      throw error;
    }
  }

  /**
   * 查询GRSAI结果
   */
  async getGRSAIResult(id: string): Promise<any> {
    try {
      console.log(`[AI Service] Querying GRSAI result for ID: ${id}`);

      const grsaiResponse = await this.grsaiProvider.getResult(id);

      console.log(`[AI Service] GRSAI result response:`, grsaiResponse);

      return grsaiResponse;
    } catch (error) {
      console.error(`[AI Service] Error querying GRSAI result:`, error);
      throw error;
    }
  }

  /**
   * 处理文本生成
   */
  private async handleTextGeneration(request: AIRequest, taskId: string): Promise<AIResponse> {
    // 获取模型配置以确定正确的endpoint
    const model = getAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    console.log(`[AI Service] Using model config endpoint: ${model.api_endpoint} for model: ${request.model}`);

    const grsaiRequest: GRSAIUnifiedRequest = {
      model: request.model,
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant.'
        },
        {
          role: 'user',
          content: request.prompt
        }
      ],
      stream: request.options?.stream || false,
      temperature: request.options?.temperature,
      max_tokens: request.options?.max_tokens
    };

    console.log(`[AI Service] Calling GRSAI unified method with endpoint: ${model.api_endpoint}`);
    
    try {
      const response = await this.grsaiProvider.makeGenerationRequest(
        model.api_endpoint,
        grsaiRequest,
        { stream: request.options?.stream || false }
      );
      
      // 检查是否是模型不存在的错误
      if (response && typeof response === 'object' && 'code' in response && response.code === -1) {
        const errorMsg = (response as any).msg || '';
        if (errorMsg.includes('不存在该模型')) {
          console.error(`[AI Service] Model ${request.model} is not available on GRSAI`);
          throw new Error(`模型 ${request.model} 暂时不可用，请选择其他模型`);
        }
      }
      
      return this.processTextResponse(response, taskId, request);
    } catch (error) {
      console.error(`[AI Service] Text generation failed:`, error);
      throw error;
    }
  }

  /**
   * 处理文本响应
   */
  private processTextResponse(response: any, taskId: string, request: AIRequest): AIResponse {
    if (response instanceof ReadableStream) {
      // 处理流式响应
      return {
        id: taskId,
        task_id: taskId,
        type: 'text',
        status: 'pending',
        result: { text: '' } // 流式响应需要客户端处理
      };
    } else {
      const textResponse = response as GRSAITextResponse;
      const outputText = textResponse.choices[0]?.message?.content || '';
      
      return {
        id: taskId,
        task_id: taskId,
        type: 'text',
        status: 'success',
        result: {
          text: outputText
        },
        usage: {
          input_tokens: this.estimateTokens(request.prompt),
          output_tokens: this.estimateTokens(outputText),
          total_tokens: 0,
          credits_consumed: 0 // 将在后续计算
        }
      };
    }
  }

  /**
   * 处理图像生成
   */
  private async handleImageGeneration(request: AIRequest, taskId: string): Promise<AIResponse> {
    console.log(`[AI Service] Starting image generation for model: ${request.model}`);

    const model = getAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    console.log(`[AI Service] Model config:`, model);

    // 合并参考图片和上传图片
    const allReferenceImages = [
      ...(request.options?.referenceImages || []),
      ...(request.options?.uploadedImages || [])
    ];

    console.log(`[AI Service] Reference images count:`, request.options?.referenceImages?.length || 0);
    console.log(`[AI Service] Uploaded images count:`, request.options?.uploadedImages?.length || 0);
    console.log(`[AI Service] All reference images:`, allReferenceImages.map(url =>
      url.startsWith('data:') ? url.substring(0, 50) + '...' : url
    ));

    console.log(`[AI Service] Using model config endpoint: ${model.api_endpoint} for image generation`);

    // 构建统一的请求对象，包含所有可能的参数
    const grsaiRequest: GRSAIUnifiedRequest = {
      model: request.model,
      prompt: request.prompt,
      urls: allReferenceImages.length > 0 ? allReferenceImages : undefined,
      // Flux 特定参数
      seed: request.options?.seed,
      image_size: request.options?.aspectRatio || request.options?.size,
      // Sora/GPT-4o 特定参数
      size: request.options?.size,
      variants: request.options?.variants,
      // 通用参数
      webHook: '-1', // 强制使用轮询模式
      cdn: request.options?.cdn || 'global'
    };

    console.log(`[AI Service] Calling GRSAI unified method with endpoint: ${model.api_endpoint}`, {
      ...grsaiRequest,
      urls: grsaiRequest.urls?.map(url =>
        url.startsWith('data:') ? url.substring(0, 50) + '...' : url
      )
    });

    const response = await this.grsaiProvider.makeGenerationRequest(
      model.api_endpoint,
      grsaiRequest
    );

    console.log(`[AI Service] GRSAI response:`, response);

    // 检查响应类型，确保不是流
    if (response instanceof ReadableStream) {
      throw new Error('Unexpected stream response for image generation');
    }

    // 检查是否是任务创建响应（包含code和data.id）
    if (response.code === 0 && response.data && response.data.id) {
      console.log(`[AI Service] Task created with ID: ${response.data.id}`);

      // 更新使用记录，保存GRSAI任务ID
      await updateAIModelUsageByTaskId(taskId, {
        external_request_id: response.data.id as string,
        response_data: response,
        status: 'pending'
      });

      // 这是任务创建响应，返回pending状态
      return {
        id: taskId,
        task_id: taskId,
        type: 'image',
        status: 'pending',
        progress: 0,
        usage: {
          credits_consumed: model.credits_per_unit
        }
      };
    }

    console.log(`[AI Service] Direct result response, status: ${response.status}`);
    // 直接返回结果的情况
    return {
      id: taskId,
      task_id: taskId,
      type: 'image',
      status: response.status === 'success' ? 'success' :
              response.status === 'failed' ? 'failed' : 'running',
      progress: response.progress || 0,
      result: {
        images: response.status === 'success' && response.data?.urls ? 
          response.data.urls.map((url: string) => ({
            url,
            width: 1024,
            height: 1024
          })) : undefined
      },
      error: response.status === 'failed' ? {
        reason: 'error',
        detail: response.error || 'Unknown error'
      } : undefined,
      usage: {
        credits_consumed: model.credits_per_unit
      }
    };
  }

  /**
   * 处理视频生成
   */
  private async handleVideoGeneration(request: AIRequest, taskId: string): Promise<AIResponse> {
    const model = getAIModelById(request.model);
    if (!model) {
      throw new Error(`Model ${request.model} not found`);
    }

    console.log(`[AI Service] Using model config endpoint: ${model.api_endpoint} for video generation`);

    // 优先使用上传的图片作为首帧，如果没有则使用指定的首帧URL
    const firstFrameUrl = request.options?.uploadedImages?.[0] || request.options?.firstFrameUrl;

    const grsaiRequest: GRSAIUnifiedRequest = {
      model: request.model,
      prompt: request.prompt,
      urls: firstFrameUrl ? [firstFrameUrl] : undefined,
      duration: request.options?.duration,
      aspect_ratio: request.options?.aspect_ratio,
      webHook: '-1', // 强制使用轮询模式
      cdn: request.options?.cdn || 'global'
    };

    console.log(`[AI Service] Calling GRSAI unified method with endpoint: ${model.api_endpoint}`);

    const response = await this.grsaiProvider.makeGenerationRequest(
      model.api_endpoint,
      grsaiRequest
    );

    // 检查响应类型，确保不是流
    if (response instanceof ReadableStream) {
      throw new Error('Unexpected stream response for video generation');
    }

    // 检查是否是任务创建响应（包含code和data.id）
    if ('code' in response && 'data' in response && response.code === 0 && response.data && typeof response.data === 'object' && response.data !== null && 'id' in response.data) {
      // 这是任务创建响应，返回pending状态
      return {
        id: taskId,
        task_id: taskId,
        type: 'video',
        status: 'pending',
        progress: 0,
        usage: {
          credits_consumed: model?.credits_per_unit || 0
        }
      };
    }

    // 直接返回结果的情况
    return {
      id: taskId,
      task_id: taskId,
      type: 'video',
      status: response.status === 'success' ? 'success' : 'failed',
      result: {
        video: response.status === 'success' && response.data?.urls?.[0] ? {
          url: response.data.urls[0],
          duration: 0
        } : undefined
      },
      usage: {
        credits_consumed: model.credits_per_unit
      }
    };
  }

  /**
   * 估算请求成本
   */
  private estimateCost(request: AIRequest, model: any): number {
    switch (request.type) {
      case 'text':
        const inputTokens = this.estimateTokens(request.prompt);
        const maxTokens = request.options?.max_tokens || 1000;
        return calculateModelCost(model.credits_per_unit, inputTokens, maxTokens);
      case 'image':
        const variants = request.options?.variants || 1;
        return model.credits_per_unit * variants;
      case 'video':
        return model.credits_per_unit;
      default:
        return model.credits_per_unit;
    }
  }

  /**
   * 估算token数量
   */
  private estimateTokens(text: string): number {
    // 简单估算：中文按字符数，英文按单词数 * 1.3
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = text.replace(/[\u4e00-\u9fff]/g, '').split(/\s+/).filter(w => w.length > 0).length;
    return Math.ceil(chineseChars + englishWords * 1.3);
  }

  /**
   * 计算输出大小
   */
  private calculateOutputSize(response: AIResponse): number {
    switch (response.type) {
      case 'text':
        return this.estimateTokens(response.result?.text || '');
      case 'image':
        return response.result?.images?.length || 0;
      case 'video':
        return response.result?.video ? 1 : 0;
      default:
        return 0;
    }
  }
}
