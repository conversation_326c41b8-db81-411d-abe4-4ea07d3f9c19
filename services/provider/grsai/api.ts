/**
 * GRSAI API 客户端和配置
 */

import type {
  GRSAITextRequest,
  GRSAITextResponse,
  GRSAIImageRequest,
  GRSAIImageResponse,
  GRSAIFluxRequest,
  GRSAIFluxResponse,
  GRSAIVideoRequest,
  GRSAIVideoResponse,
  GRSAIResultResponse,
  GRSAIUnifiedRequest,
  GRSAIUnifiedResponse
} from './api-types';

/**
 * GRSAI API 配置
 */
export const GRSAI_CONFIG = {
  baseURL: {
    overseas: 'https://api.grsai.com',
    domestic: 'https://grsai.dakka.com.cn'
  },
  defaultRegion: 'overseas' as const, // 默认使用海外节点
  timeout: 60000, // 60秒超时
  retryAttempts: 3,
  retryDelay: 1000
} as const;

export type GRSAIRegion = 'overseas' | 'domestic';

/**
 * GRSAI API 客户端
 */
export class GRSAIProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey?: string, region: GRSAIRegion = 'overseas') {
    this.apiKey = apiKey || process.env.GRSAI_APIKEY || '';
    this.baseURL = GRSAI_CONFIG.baseURL[region];

    if (!this.apiKey) {
      throw new Error('GRSAI API key is required');
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest(
    endpoint: string,
    data: any,
    options: { stream?: boolean } = {}
  ): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };

    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    };

    console.log(`[GRSAI] Making request to: ${url}`);

    // 创建一个安全的数据副本用于日志，截断base64图片数据
    const safeData = { ...data };
    if (safeData.urls && Array.isArray(safeData.urls)) {
      safeData.urls = safeData.urls.map((url: string) =>
        url.startsWith('data:') ? url.substring(0, 50) + '...' : url
      );
    }
    console.log(`[GRSAI] Request data:`, JSON.stringify(safeData, null, 2));

    let attempt = 0;
    while (attempt < GRSAI_CONFIG.retryAttempts) {
      try {
        const response = await fetch(url, requestOptions);

        console.log(`[GRSAI] Response status: ${response.status}`);
        console.log(`[GRSAI] Response headers:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const errorText = await response.text();
          console.log(`[GRSAI] Error response:`, errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        if (options.stream) {
          return response; // 返回流响应
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        console.log(`[GRSAI] Content-Type: ${contentType}`);

        if (contentType && contentType.includes('text/plain')) {
          // 处理SSE格式的响应
          const text = await response.text();
          console.log(`[GRSAI] SSE response text:`, text);
          return this.parseSSEResponse(text);
        }

        const jsonResponse = await response.json();
        console.log(`[GRSAI] JSON response:`, JSON.stringify(jsonResponse, null, 2));
        return jsonResponse;
      } catch (error) {
        console.log(`[GRSAI] Request attempt ${attempt + 1} failed:`, error);
        attempt++;
        if (attempt >= GRSAI_CONFIG.retryAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, GRSAI_CONFIG.retryDelay * attempt));
      }
    }
  }

  /**
   * 解析SSE响应
   */
  private parseSSEResponse(text: string): any {
    try {
      console.log(`[GRSAI] ========== PARSING SSE RESPONSE ==========`);
      console.log(`[GRSAI] Raw SSE text:`, text);

      const lines = text.split('\n');
      let lastJsonData: any = null;
      let validDataCount = 0;

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const dataStr = line.slice(6).trim();
          if (dataStr === '[DONE]') {
            console.log(`[GRSAI] SSE stream completed`);
            break;
          }

          try {
            const jsonData = JSON.parse(dataStr);
            lastJsonData = jsonData;
            validDataCount++;
            console.log(`[GRSAI] SSE data ${validDataCount}:`, jsonData);
          } catch (e) {
            console.log(`[GRSAI] Failed to parse SSE data:`, dataStr);
          }
        }
      }

      console.log(`[GRSAI] Found ${validDataCount} valid JSON data entries`);
      console.log(`[GRSAI] Final parsed data:`, lastJsonData);

      return lastJsonData || { error: 'No valid JSON data found in SSE response' };
    } catch (error) {
      console.log(`[GRSAI] SSE parsing error:`, error);
      throw new Error(`Failed to parse SSE response: ${error}`);
    }
  }



  /**
   * 查询结果
   */
  async getResult(id: string): Promise<GRSAIResultResponse> {
    const endpoint = '/v1/draw/result';
    return await this.makeRequest(endpoint, { id });
  }

  /**
   * 统一的生成请求方法 - 基于模型配置的endpoint
   * 这是新的统一接口，将逐步替代上面的特定方法
   */
  async makeGenerationRequest(
    endpoint: string,
    request: GRSAIUnifiedRequest,
    options: { stream?: boolean } = {}
  ): Promise<GRSAIUnifiedResponse | ReadableStream> {
    // 对于异步任务（非文本生成），设置webHook为-1以获取任务ID
    const isTextGeneration = endpoint === '/v1/chat/completions';
    const requestWithWebhook = isTextGeneration
      ? request
      : { ...request, webHook: request.webHook || '-1' };

    console.log(`[GRSAI] Using unified method for endpoint: ${endpoint}`);
    return await this.makeRequest(endpoint, requestWithWebhook, options);
  }
}