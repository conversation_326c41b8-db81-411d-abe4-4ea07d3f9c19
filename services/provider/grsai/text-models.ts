/**
 * GRSAI 文本和多模态模型配置
 */

import { AIModelConfig } from '../types/model';

/**
 * GRSAI 文本和多模态模型配置
 */
export const GRSAI_TEXT_MODELS: AIModelConfig[] = [
  // 文本生成模型
  {
    model_id: 'gemini-2.5-pro',
     model_name: '',
    model_type: 'text',
    provider: 'grsai',
    api_endpoint: '/v1/chat/completions',
    credits_per_unit: 10,
    unit_type: 'tokens',
    is_active: true,
    translationKey: 'grsai.gemini-2-5-pro',
    max_input_size: 128000,
    supported_features: ['text_generation', 'conversation', 'analysis'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 8192,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },
  
  {
    model_id: 'gemini-2.5-flash',
     model_name: '',
    model_type: 'text',
    provider: 'grsai',
    api_endpoint: '/v1/chat/completions',
    credits_per_unit: 5,
    unit_type: 'tokens',
    is_active: true,
    translationKey: 'grsai.gemini-2-5-flash',
    max_input_size: 128000,
    supported_features: ['text_generation', 'conversation', 'fast_response'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['stream'],
      expert: []
    }
  },
  
  {
    model_id: 'gemini-2.5-flash-lite',
     model_name: '',
    model_type: 'text',
    provider: 'grsai',
    api_endpoint: '/v1/chat/completions',
    credits_per_unit: 2,
    unit_type: 'tokens',
    is_active: true,
    translationKey: 'grsai.gemini-2-5-flash-lite',
    max_input_size: 64000,
    supported_features: ['text_generation', 'basic_conversation'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 500,
        min: 1,
        max: 2048,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      }
    ],
    parameter_groups: {
      basic: ['max_tokens', 'temperature'],
      advanced: [],
      expert: []
    }
  },
  
  {
    model_id: 'gpt-4o-mini',
     model_name: '',
    model_type: 'text',
    provider: 'grsai',
    api_endpoint: '/v1/chat/completions',
    credits_per_unit: 8,
    unit_type: 'tokens',
    is_active: true,
    translationKey: 'grsai.gpt-4o-mini',
    max_input_size: 128000,
    supported_features: ['text_generation', 'conversation', 'reasoning'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['max_tokens', 'temperature'],
      advanced: ['top_p', 'stream'],
      expert: []
    }
  },

  // 多模态模型
  {
    model_id: 'o4-mini-all',
     model_name: '',
    model_type: 'multimodal',
    provider: 'grsai',
    api_endpoint: '/v1/chat/completions',
    credits_per_unit: 12,
    unit_type: 'tokens',
    is_active: true,
    translationKey: 'grsai.o4-mini-all',
    max_input_size: 128000,
    supported_features: ['text_generation', 'vision', 'multimodal', 'reasoning'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 2048,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto' },
          { value: 'low' },
          { value: 'high' }
        ],
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'stream'],
      expert: []
    }
  },

  {
    model_id: 'gpt-4o-all',
     model_name: '',
    model_type: 'multimodal',
    provider: 'grsai',
    api_endpoint: '/v1/chat/completions',
    credits_per_unit: 20,
    unit_type: 'tokens',
    is_active: true,
    translationKey: 'grsai.gpt-4o-all',
    max_input_size: 128000,
    supported_features: ['text_generation', 'vision', 'multimodal', 'advanced_reasoning', 'code_generation'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'max_tokens',
        type: 'number',
        required: false,
        default: 1000,
        min: 1,
        max: 4096,
        group: 'basic'
      },
      {
        name: 'temperature',
        type: 'number',
        required: false,
        default: 0.7,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'vision_detail',
        type: 'select',
        required: false,
        default: 'auto',
        options: [
          { value: 'auto' },
          { value: 'low' },
          { value: 'high' }
        ],
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'top_p',
        type: 'number',
        required: false,
        default: 0.9,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'stream',
        type: 'boolean',
        required: false,
        default: false,
        group: 'advanced'
      }
    ],
    parameter_groups: {
      basic: ['max_tokens', 'temperature', 'uploadedImages'],
      advanced: ['vision_detail', 'top_p', 'stream'],
      expert: []
    }
  }
];