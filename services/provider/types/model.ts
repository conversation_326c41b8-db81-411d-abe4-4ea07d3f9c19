/**
 * 统一的AI模型配置接口
 * 所有provider都使用这个接口，避免重复转换
 */

import { ParameterConfig } from '../types';

export interface AIModelConfig {
  // 基础信息
  model_id: string;
  model_name: string;
  model_type: 'text' | 'image' | 'video' | 'multimodal';
  provider: string;
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: string;
  is_active: boolean;
  
  // 可选信息
  description?: string;
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  translationKey: string;
  
  // 参数配置
  parameters: ParameterConfig[];
  parameter_groups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
  
  // 兼容字段（用于数据库）
  created_at?: string;
  updated_at?: string;
}

// 模型类型枚举
export enum ModelType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  MULTIMODAL = 'multimodal'
}

// 提供商枚举
export enum Provider {
  GRSAI = 'grsai',
  REPLICATE = 'replicate',
  VOLCENGINE = 'volcengine'
}

// 单位类型枚举
export enum UnitType {
  TOKENS = 'tokens',
  IMAGES = 'images',
  VIDEOS = 'videos'
}