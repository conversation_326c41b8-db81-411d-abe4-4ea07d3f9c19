/**
 * 模型参数配置类型定义
 */

export interface ParameterConfig {
  name: string;
  type: 'number' | 'string' | 'select' | 'boolean' | 'range' | 'file';
  required: boolean;
  default?: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{
    value: string;
    label?: string; // 可选，运行时从翻译文件获取
    description?: string; // 可选，运行时从翻译文件获取
  }>;
  description?: string; // 可选，运行时从翻译文件获取
  tooltip?: string; // 可选，运行时从翻译文件获取
  group: 'basic' | 'advanced' | 'expert';
  dependsOn?: string; // 依赖其他参数
  condition?: {
    field: string;
    value: any;
    operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'not_empty';
  };
}

export interface ModelParameterConfig {
  modelId: string;
  version: string;
  provider: string;
  modelType: 'text' | 'image' | 'video' | 'multimodal';
  parameters: ParameterConfig[];
  parameterGroups: {
    basic: string[];
    advanced: string[];
    expert: string[];
  };
}

/**
 * 参数组标题映射 - 使用翻译key
 */
export const PARAMETER_GROUP_TITLES = {
  basic: 'common.parameterGroups.basic',
  advanced: 'common.parameterGroups.advanced',
  expert: 'common.parameterGroups.expert'
} as const;

/**
 * 通用的宽高比选项 - 使用翻译key
 */
export const ASPECT_RATIO_OPTIONS = [
  { value: '1:1', labelKey: 'common.options.aspectRatio.1:1.label', descriptionKey: 'common.options.aspectRatio.1:1.description' },
  { value: '16:9', labelKey: 'common.options.aspectRatio.16:9.label', descriptionKey: 'common.options.aspectRatio.16:9.description' },
  { value: '9:16', labelKey: 'common.options.aspectRatio.9:16.label', descriptionKey: 'common.options.aspectRatio.9:16.description' },
  { value: '4:3', labelKey: 'common.options.aspectRatio.4:3.label', descriptionKey: 'common.options.aspectRatio.4:3.description' },
  { value: '3:2', labelKey: 'common.options.aspectRatio.3:2.label', descriptionKey: 'common.options.aspectRatio.3:2.description' },
  { value: '21:9', labelKey: 'common.options.aspectRatio.21:9.label', descriptionKey: 'common.options.aspectRatio.21:9.description' },
  { value: '2:3', labelKey: 'common.options.aspectRatio.2:3.label', descriptionKey: 'common.options.aspectRatio.2:3.description' },
  { value: '4:5', labelKey: 'common.options.aspectRatio.4:5.label', descriptionKey: 'common.options.aspectRatio.4:5.description' },
  { value: '5:4', labelKey: 'common.options.aspectRatio.5:4.label', descriptionKey: 'common.options.aspectRatio.5:4.description' },
  { value: '3:4', labelKey: 'common.options.aspectRatio.3:4.label', descriptionKey: 'common.options.aspectRatio.3:4.description' }
];

/**
 * 通用的图片数量选项 - 使用翻译key
 */
export const VARIANTS_OPTIONS = [
  { value: '1', labelKey: 'common.options.variants.1.label', descriptionKey: 'common.options.variants.1.description' },
  { value: '2', labelKey: 'common.options.variants.2.label', descriptionKey: 'common.options.variants.2.description' },
  { value: '3', labelKey: 'common.options.variants.3.label', descriptionKey: 'common.options.variants.3.description' },
  { value: '4', labelKey: 'common.options.variants.4.label', descriptionKey: 'common.options.variants.4.description' }
];

/**
 * 通用的输出格式选项 - 使用翻译key
 */
export const OUTPUT_FORMAT_OPTIONS = [
  { value: 'webp', labelKey: 'common.options.outputFormat.webp.label', descriptionKey: 'common.options.outputFormat.webp.description' },
  { value: 'jpg', labelKey: 'common.options.outputFormat.jpg.label', descriptionKey: 'common.options.outputFormat.jpg.description' },
  { value: 'png', labelKey: 'common.options.outputFormat.png.label', descriptionKey: 'common.options.outputFormat.png.description' }
];

/**
 * 通用的图片尺寸选项（Replicate）- 使用翻译key
 */
export const MEGAPIXELS_OPTIONS = [
  { value: '0.25', labelKey: 'common.options.megapixels.0_25.label', descriptionKey: 'common.options.megapixels.0_25.description' },
  { value: '1', labelKey: 'common.options.megapixels.1.label', descriptionKey: 'common.options.megapixels.1.description' }
];

/**
 * 通用的CDN选项 - 使用翻译key
 */
export const CDN_OPTIONS = [
  { value: 'global', labelKey: 'common.options.cdn.global.label', descriptionKey: 'common.options.cdn.global.description' },
  { value: 'zh', labelKey: 'common.options.cdn.zh.label', descriptionKey: 'common.options.cdn.zh.description' }
];

/**
 * 通用的图片尺寸选项 - 使用翻译key
 */
export const SIZE_OPTIONS = [
  { value: '1024x1024', labelKey: 'common.options.size.1024x1024.label', descriptionKey: 'common.options.size.1024x1024.description' },
  { value: '1792x1024', labelKey: 'common.options.size.1792x1024.label', descriptionKey: 'common.options.size.1792x1024.description' },
  { value: '1024x1792', labelKey: 'common.options.size.1024x1792.label', descriptionKey: 'common.options.size.1024x1792.description' }
];

/**
 * 通用的视频时长选项 - 使用翻译key
 */
export const DURATION_OPTIONS = [
  { value: '5', labelKey: 'common.options.duration.5.label', descriptionKey: 'common.options.duration.5.description' },
  { value: '10', labelKey: 'common.options.duration.10.label', descriptionKey: 'common.options.duration.10.description' },
  { value: '15', labelKey: 'common.options.duration.15.label', descriptionKey: 'common.options.duration.15.description' }
];

/**
 * 通用的视频分辨率选项 - 使用翻译key
 */
export const RESOLUTION_OPTIONS = [
  { value: '720p', labelKey: 'common.options.resolution.720p.label', descriptionKey: 'common.options.resolution.720p.description' },
  { value: '1080p', labelKey: 'common.options.resolution.1080p.label', descriptionKey: 'common.options.resolution.1080p.description' },
  { value: '4k', labelKey: 'common.options.resolution.4k.label', descriptionKey: 'common.options.resolution.4k.description' }
];

/**
 * 通用的帧率选项 - 使用翻译key
 */
export const FPS_OPTIONS = [
  { value: '24', labelKey: 'common.options.fps.24.label', descriptionKey: 'common.options.fps.24.description' },
  { value: '30', labelKey: 'common.options.fps.30.label', descriptionKey: 'common.options.fps.30.description' },
  { value: '60', labelKey: 'common.options.fps.60.label', descriptionKey: 'common.options.fps.60.description' }
];

/**
 * 通用的质量选项 - 使用翻译key
 */
export const QUALITY_OPTIONS = [
  { value: 'standard', labelKey: 'common.options.quality.standard.label', descriptionKey: 'common.options.quality.standard.description' },
  { value: 'high', labelKey: 'common.options.quality.high.label', descriptionKey: 'common.options.quality.high.description' },
  { value: 'ultra', labelKey: 'common.options.quality.ultra.label', descriptionKey: 'common.options.quality.ultra.description' }
];

/**
 * 通用的布尔值选项 - 使用翻译key
 */
export const BOOLEAN_OPTIONS = [
  { value: 'true', labelKey: 'common.options.boolean.true.label', descriptionKey: 'common.options.boolean.true.description' },
  { value: 'false', labelKey: 'common.options.boolean.false.label', descriptionKey: 'common.options.boolean.false.description' }
];
