import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
  handleSubscriptionCredits,
} from "./credit";
import {
  findOrderByOrderNo,
  updateOrderStatus,
  findOrderByPaymentSession,
  findOrderBySubscriptionId,
  updateOrderSubscription,
  updateSubscriptionStatus,
  updateOrderProduct,
  checkTransactionProcessed
} from "@/models/order";
import { getSupabaseClient } from "@/models/db";
import { getIsoTimestr } from "@/lib/time";

import Stripe from "stripe";
import { updateAffiliateForOrder } from "./affiliate";
import { Order } from "@/types/order";
import { getProductCreditsFromPricing } from "@/lib/pricing-utils";

export async function handleOrderSession(session: Stripe.Checkout.Session) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paied order
        await updateCreditForOrder(order);
      }

      // update affiliate for paied order
      await updateAffiliateForOrder(order);
    }

    console.log(
      "handle order session successed: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle order session failed: ", e);
    throw e;
  }
}

export async function handlePaymentCallback(
  provider: string,
  sessionId: string,
  orderNo?: string,
  eventType?: string,
  subscriptionData?: any,
  isSubscription?: boolean,
  transactionId?: string,
  additionalData?: any
) {
  try {
    console.log(`[PAYMENT_CALLBACK] Processing ${eventType} for ${provider}`);

    switch (eventType) {
      case 'checkout.completed':
        if (isSubscription) {
          // 首次订阅：忽略此事件，等待 subscription.paid
          console.log(`Ignoring checkout.completed for subscription: ${orderNo}`);
          return;
        } else {
          // 单次付款处理
          await handleOneTimePayment(provider, sessionId, orderNo!, transactionId);
        }
        break;

      case 'subscription.paid':
        await handleSubscriptionPayment(subscriptionData, transactionId);
        break;

      case 'subscription.canceled':
        await handleSubscriptionCanceled(subscriptionData, additionalData?.canceledAt);
        break;

      case 'subscription.expired':
        await handleSubscriptionExpired(subscriptionData);
        break;

      case 'subscription.update':
        await handleSubscriptionUpdate(subscriptionData);
        break;

      case 'subscription.trialing':
        await handleSubscriptionTrialing(subscriptionData);
        break;

      case 'subscription.active':
        // 仅记录日志，不处理业务逻辑
        console.log('Subscription active event received for sync');
        break;

      default:
        // 兼容旧版本调用（没有eventType的情况）
        await handleLegacyPaymentCallback(provider, sessionId, orderNo);
        break;
    }
  } catch (error) {
    console.error(`Failed to process payment callback:`, error);
    throw error;
  }
}

// 处理单次付款
async function handleOneTimePayment(
  provider: string,
  sessionId: string,
  orderNo: string,
  transactionId?: string
) {
  // 查找订单
  const order = await findOrderByOrderNo(orderNo);
  if (!order || order.status !== "created") {
    if (order?.status === "paid") {
      console.log(`Order already paid, skipping processing`);
      return;
    }
    throw new Error(`Order not found or invalid status`);
  }

  // 更新订单状态
  const paid_at = getIsoTimestr();
  const paid_detail = JSON.stringify({
    provider,
    sessionId,
    orderNo,
    processedVia: 'webhook',
    timestamp: paid_at
  });

  await updateOrderStatus(order.order_no, "paid", paid_at, order.user_email, paid_detail);

  // 发放积分：单次付款积分永不过期
  if (order.credits > 0) {
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: null, // 单次付款积分永不过期
      order_no: order.order_no,
      transaction_id: transactionId, // 传递transaction ID用于去重
    });
  }

  await updateAffiliateForOrder(order);
  console.log(`One-time payment processed: ${order.order_no}`);
}

// 处理首次订阅付款
async function handleFirstSubscriptionPayment(
  provider: string,
  sessionId: string,
  orderNo: string,
  subscriptionData: any,
  transactionId?: string
) {
  // 查找订单
  const order = await findOrderByOrderNo(orderNo);
  if (!order || order.status !== "created") {
    if (order?.status === "paid") {
      console.log(`Order already paid, skipping processing`);
      return;
    }
    throw new Error(`Order not found or invalid status`);
  }

  // 更新订单状态
  const paid_at = getIsoTimestr();
  const paid_detail = JSON.stringify({
    provider,
    sessionId,
    orderNo,
    subscriptionData,
    processedVia: 'webhook',
    timestamp: paid_at
  });

  await updateOrderStatus(order.order_no, "paid", paid_at, order.user_email, paid_detail);

  // 保存订阅信息
  const periodStart = new Date(subscriptionData.current_period_start_date).getTime() / 1000;
  const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;

  await updateOrderSubscription(
    order.order_no,
    subscriptionData.id,
    1, // sub_interval_count
    periodStart, // sub_cycle_anchor
    periodEnd, // sub_period_end
    periodStart, // sub_period_start
    'paid',
    paid_at,
    1, // sub_times (首次)
    order.user_email,
    JSON.stringify(subscriptionData)
  );

  // 发放积分：订阅积分在周期结束时过期
  if (order.credits > 0) {
    const expiredAt = new Date(periodEnd * 1000).toISOString();
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: expiredAt, // 订阅积分在当前周期结束时过期
      order_no: order.order_no,
      transaction_id: transactionId, // 传递transaction ID用于去重
    });
  }

  await updateAffiliateForOrder(order);
  console.log(`First subscription payment processed: ${order.order_no}`);
}

// 处理订阅付款（含去重逻辑）
async function handleSubscriptionPayment(subscriptionData: any, transactionId?: string) {
  try {
    const subscriptionId = subscriptionData.id;
    // 优先使用传入的transactionId，否则从subscriptionData中获取
    const finalTransactionId = transactionId || subscriptionData.last_transaction_id;

    // 1. 查找对应的订单
    // 首先尝试通过subscription ID查找（适用于续费）
    let order = await findOrderBySubscriptionId(subscriptionId);

    // 如果通过subscription ID找不到订单，尝试通过metadata中的order_no查找（适用于首次订阅）
    if (!order && subscriptionData.metadata?.order_no) {
      order = await findOrderByOrderNo(subscriptionData.metadata.order_no);
    }

    if (!order) {
      throw new Error(`Order not found for subscription: ${subscriptionId}, order_no: ${subscriptionData.metadata?.order_no || 'N/A'}`);
    }

    // 2. 检查是否已处理过此次交易（基于transaction ID去重）
    if (finalTransactionId) {
      const existingTransaction = await checkTransactionProcessedLocal(order.order_no, finalTransactionId);
      if (existingTransaction) {
        console.log(`Transaction already processed: ${finalTransactionId}`);
        return;
      }
    }

    // 3. 判断是首次订阅还是续费
    const isFirstSubscription = order.status === 'created';
    const periodStart = new Date(subscriptionData.current_period_start_date).getTime() / 1000;
    const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;
    const currentSubTimes = isFirstSubscription ? 1 : (order.sub_times || 0) + 1;

    console.log(`Processing subscription payment: ${subscriptionId}, isFirst: ${isFirstSubscription}, times: ${currentSubTimes}`);

    // 4. 更新订阅信息（包括设置 sub_status 为 active）
    await updateOrderSubscription(
      order.order_no,
      subscriptionId,
      1, // sub_interval_count
      periodStart, // sub_cycle_anchor
      periodEnd, // sub_period_end
      periodStart, // sub_period_start
      'paid', // status
      getIsoTimestr(), // paid_at
      currentSubTimes, // sub_times
      order.user_email, // paid_email
      JSON.stringify({ ...subscriptionData, transaction_id: finalTransactionId }), // paid_detail
      'active' // 🔥 设置 sub_status 为 active
    );

    // 5. 发放积分
    if (order.credits > 0) {
      const expiredAt = new Date(periodEnd * 1000).toISOString();
      if (isFirstSubscription) {
        // 首次订阅：直接发放积分
        await increaseCredits({
          user_uuid: order.user_uuid,
          trans_type: CreditsTransType.OrderPay,
          credits: order.credits,
          expired_at: expiredAt,
          order_no: order.order_no,
          transaction_id: finalTransactionId,
        });
      } else {
        // 续费：使用去重逻辑发放积分
        await handleSubscriptionCredits(order, periodEnd, finalTransactionId);
      }
    }

    // 6. 处理推荐奖励（仅首次订阅）
    if (isFirstSubscription) {
      await updateAffiliateForOrder(order);
    }

    console.log(`Subscription payment processed: ${subscriptionId}, isFirst: ${isFirstSubscription}, times: ${currentSubTimes}`);
  } catch (error) {
    console.error('Failed to handle subscription payment:', error);
    throw error;
  }
}

// 检查交易是否已处理（移动到这里避免重复定义）
async function checkTransactionProcessedLocal(orderNo: string, transactionId: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("paid_detail")
    .eq("order_no", orderNo)
    .single();

  if (error || !data) return false;

  try {
    const paidDetail = JSON.parse(data.paid_detail || '{}');
    return paidDetail.transaction_id === transactionId;
  } catch {
    return false;
  }
}

// 兼容旧版本的支付回调处理
async function handleLegacyPaymentCallback(
  provider: string,
  sessionId: string,
  orderNo?: string
) {
  console.log(`[LEGACY_PAYMENT_CALLBACK] Processing ${provider} callback:`, {
    sessionId,
    orderNo,
    provider
  });

  // 查找订单
  let order: Order | undefined;
  if (orderNo) {
    order = await findOrderByOrderNo(orderNo);
  } else {
    order = await findOrderByPaymentSession(sessionId, provider);
  }

  if (!order) {
    throw new Error("Order not found");
  }

  if (order.status !== "created") {
    if (order.status === "paid") {
      console.log(`Order already paid, skipping processing`);
      return;
    }
    throw new Error(`Order status is ${order.status}, expected 'created'`);
  }

  // 更新订单状态
  const paid_at = getIsoTimestr();
  const paid_detail = JSON.stringify({
    provider,
    sessionId,
    orderNo,
    processedVia: 'webhook',
    timestamp: paid_at
  });

  await updateOrderStatus(order.order_no, "paid", paid_at, order.user_email, paid_detail);

  // 发放积分和处理分销
  if (order.credits > 0) {
    await updateCreditForOrder(order);
  }

  await updateAffiliateForOrder(order);
  console.log(`Legacy payment processed: ${order.order_no}`);
}

// 处理订阅取消
async function handleSubscriptionCanceled(subscriptionData: any, canceledAt?: string) {
  try {
    const subscriptionId = subscriptionData.id;
    const order = await findOrderBySubscriptionId(subscriptionId);

    if (!order) {
      throw new Error(`Order not found for canceled subscription: ${subscriptionId}`);
    }

    console.log(`Processing subscription cancellation: ${subscriptionId}`);

    const cancelTime = canceledAt || new Date().toISOString();

    // 更新订阅状态为 canceled
    await updateSubscriptionStatus(
      order.order_no,
      'canceled',
      cancelTime,
      JSON.stringify({ ...subscriptionData, event: 'canceled' })
    );

    console.log(`Subscription canceled: ${subscriptionId}, user can use until period end`);
  } catch (error) {
    console.error('Failed to handle subscription cancellation:', error);
    throw error;
  }
}

// 处理订阅过期（付款失败，可能重试）
async function handleSubscriptionExpired(subscriptionData: any) {
  try {
    const subscriptionId = subscriptionData.id;
    const order = await findOrderBySubscriptionId(subscriptionId);

    if (!order) {
      throw new Error(`Order not found for expired subscription: ${subscriptionId}`);
    }

    console.log(`Processing subscription expiration: ${subscriptionId}`);

    // 判断是否为试用期付款失败
    const wasTrialing = order.sub_status === 'trialing';

    if (wasTrialing) {
      console.log(`Trial period payment failed for subscription: ${subscriptionId}`);

      // 试用期付款失败 - 注意：这里不是真正的"过期"，可能会重试付款
      await updateSubscriptionStatus(
        order.order_no,
        'expired', // 暂时标记为过期，等待可能的付款重试
        new Date().toISOString(),
        JSON.stringify({
          ...subscriptionData,
          event: 'trial_payment_failed',
          note: 'Trial period ended, payment failed, may retry'
        })
      );

      // 发送付款失败通知（不是试用结束通知）
      await notifyPaymentFailed(order, subscriptionData, 'trial');
    } else {
      console.log(`Regular subscription payment failed: ${subscriptionId}`);

      // 常规订阅付款失败
      await updateSubscriptionStatus(
        order.order_no,
        'expired',
        new Date().toISOString(),
        JSON.stringify({
          ...subscriptionData,
          event: 'payment_failed',
          note: 'Payment failed, may retry'
        })
      );

      // 发送付款失败通知
      await notifyPaymentFailed(order, subscriptionData, 'subscription');
    }

    console.log(`Subscription expiration processed: ${subscriptionId}, wasTrialing: ${wasTrialing}`);
  } catch (error) {
    console.error('Failed to handle subscription expiration:', error);
    throw error;
  }
}

// 发送付款失败通知
async function notifyPaymentFailed(order: Order, subscriptionData: any, type: 'trial' | 'subscription') {
  try {
    console.log(`Sending payment failed notification to user: ${order.user_email}, type: ${type}`);

    const notificationData = {
      user_uuid: order.user_uuid,
      user_email: order.user_email,
      subscription_id: subscriptionData.id,
      period_end: subscriptionData.current_period_end_date,
      notification_type: type === 'trial' ? 'trial_payment_failed' : 'payment_failed',
      sent_at: new Date().toISOString(),
      message: type === 'trial'
        ? 'Your trial period has ended and payment failed. Please update your payment method.'
        : 'Your subscription payment failed. Please update your payment method.'
    };

    console.log('Payment failed notification data:', notificationData);

    // TODO: 实际的邮件发送逻辑
    // await sendEmail({
    //   to: order.user_email,
    //   subject: type === 'trial' ? 'Trial payment failed' : 'Subscription payment failed',
    //   template: 'payment_failed',
    //   data: notificationData
    // });

  } catch (error) {
    console.error('Failed to send payment failed notification:', error);
    // 通知失败不应该影响主流程
  }
}

// 处理订阅更新（升级/降级）
async function handleSubscriptionUpdate(subscriptionData: any) {
  try {
    const subscriptionId = subscriptionData.id;
    const order = await findOrderBySubscriptionId(subscriptionId);

    if (!order) {
      throw new Error(`Order not found for updated subscription: ${subscriptionId}`);
    }

    console.log(`Processing subscription update: ${subscriptionId}`);

    const newProductId = subscriptionData.product?.id || subscriptionData.items?.[0]?.product_id;
    const newCredits = await getProductCreditsLocal(newProductId);
    const creditDiff = newCredits - order.credits;

    // 只处理升级的积分补发（降级不回收积分）
    if (creditDiff > 0) {
      const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;
      const expiredAt = new Date(periodEnd * 1000).toISOString();

      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: creditDiff,
        expired_at: expiredAt,
        order_no: order.order_no,
      });

      console.log(`Plan upgrade credits granted: +${creditDiff} credits`);
    }

    // 更新订单的产品信息
    await updateOrderProduct(order.order_no, newProductId, newCredits);

    console.log(`Subscription update processed: ${subscriptionId}`);
  } catch (error) {
    console.error('Failed to handle subscription update:', error);
    throw error;
  }
}

// 处理试用期（可选）
const TRIAL_CREDITS = 100; // 固定试用积分

async function handleSubscriptionTrialing(subscriptionData: any) {
  try {
    const subscriptionId = subscriptionData.id;

    let order = await findOrderBySubscriptionId(subscriptionId);
    if (!order && subscriptionData.metadata?.order_no) {
      order = await findOrderByOrderNo(subscriptionData.metadata.order_no);
    }

    if (!order) {
      console.log(`No order found for trialing subscription: ${subscriptionId}`);
      return;
    }

    console.log(`Processing subscription trial: ${subscriptionId}`);

    // 更新订阅状态为 trialing
    await updateSubscriptionStatus(
      order.order_no,
      'trialing',
      new Date().toISOString(),
      JSON.stringify({ ...subscriptionData, event: 'trialing' })
    );

    // 发放固定试用积分
    const trialEnd = new Date(subscriptionData.current_period_end_date).toISOString();
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: TRIAL_CREDITS,
      expired_at: trialEnd,
      order_no: order.order_no,
    });

    console.log(`Trial started: ${subscriptionId}, ${TRIAL_CREDITS} credits granted`);
  } catch (error) {
    console.error('Failed to handle subscription trial:', error);
    throw error;
  }
}

// 获取产品积分数量（使用定价配置）
async function getProductCreditsLocal(productId: string): Promise<number> {
  try {
    // 使用定价配置获取产品对应的积分数量
    return getProductCreditsFromPricing(productId, 'creem');
  } catch (error) {
    console.error(`Failed to get credits for product ${productId}:`, error);
    return 1000; // 默认积分数量
  }
}
