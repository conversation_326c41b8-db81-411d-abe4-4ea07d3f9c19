import { Jim<PERSON>, loadFont } from 'jimp';
import { SANS_16_BLACK, SANS_16_WHITE, SANS_32_BLACK, SANS_32_WHITE, SANS_64_BLACK, SANS_64_WHITE, SANS_128_BLACK, SANS_128_WHITE } from 'jimp/fonts';

/**
 * 水印位置枚举
 */
export enum WatermarkPosition {
  TOP_LEFT = 'top-left',
  TOP_RIGHT = 'top-right',
  BOTTOM_LEFT = 'bottom-left',
  BOTTOM_RIGHT = 'bottom-right',
  CENTER = 'center'
}

/**
 * 水印配置接口
 */
export interface WatermarkConfig {
  text: string;
  position: WatermarkPosition;
  opacity: number; // 0-1 之间的透明度值
  fontSize?: number; // 字体大小，默认为图片宽度的 1/20
  fontSizeRatio?: number; // 字体大小相对于图片宽度的比例，默认为 0.05 (5%)
  color?: string; // 文字颜色，默认白色
  margin?: number; // 边距，默认为 20px
}

/**
 * 水印服务类
 */
export class WatermarkService {
  /**
   * 为图片添加水印
   * @param imageBuffer 原始图片的 Buffer
   * @param config 水印配置
   * @returns 添加水印后的图片 Buffer
   */
  static async addWatermark(
    imageBuffer: Buffer,
    config: WatermarkConfig
  ): Promise<Buffer> {
    try {
      console.log(`[Watermark Service] Starting watermark process`);
      console.log(`[Watermark Service] Config:`, config);

      // 读取图片
      const image = await Jimp.read(imageBuffer);
      const { width, height } = image.bitmap;

      console.log(`[Watermark Service] Image dimensions: ${width}x${height}`);

      // 智能计算字体大小
      const fontSize = this.calculateOptimalFontSize(width, height, config.text, config.fontSize, config.fontSizeRatio);
      const color = config.color || '#FFFFFF';
      const margin = config.margin || 20;
      const opacity = Math.max(0, Math.min(1, config.opacity)); // 确保在 0-1 范围内

      console.log(`[Watermark Service] Font size: ${fontSize}, Color: ${color}, Margin: ${margin}, Opacity: ${opacity}`);

      // 选择最合适的字体大小
      const { font, actualFontSize } = await this.selectBestFont(fontSize);

      console.log(`[Watermark Service] Font loaded with actual size: ${actualFontSize}`);

      // 更精确的文字尺寸估算（使用实际字体大小）
      const textMetrics = this.calculateTextDimensions(config.text, actualFontSize);
      const textWidth = textMetrics.width;
      const textHeight = textMetrics.height;

      console.log(`[Watermark Service] Calculated text dimensions: ${textWidth}x${textHeight}`);

      // 计算水印位置
      const position = this.calculatePosition(
        config.position,
        width,
        height,
        textWidth,
        textHeight,
        margin
      );

      console.log(`[Watermark Service] Watermark position: x=${position.x}, y=${position.y}`);

      // 确保位置是整数
      const startX = Math.floor(position.x);
      const startY = Math.floor(position.y);

      // 创建文字水印图层
      const textLayer = new Jimp({
        width: Math.floor(textWidth),
        height: Math.floor(textHeight),
        color: 0x00000000 // 透明背景
      });

      // 在图层上打印文字
      textLayer.print({
        font: font,
        x: 0,
        y: 0,
        text: config.text
      });

      // 应用透明度
      textLayer.opacity(opacity);

      // 将文字图层合成到原图上
      image.composite(textLayer, startX, startY);

      console.log(`[Watermark Service] Watermark applied at position (${position.x}, ${position.y})`);

      // 检测原图格式并保持格式
      const originalFormat = this.detectImageFormat(imageBuffer);
      console.log(`[Watermark Service] Original format: ${originalFormat}`);

      // 根据原格式返回相应的 Buffer
      return await this.getOptimizedBuffer(image, originalFormat);
    } catch (error) {
      console.error(`[Watermark Service] Error adding watermark:`, error);
      throw new Error(`Failed to add watermark: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 选择最合适的字体
   */
  private static async selectBestFont(targetSize: number): Promise<{ font: any; actualFontSize: number }> {
    // 可用的字体大小和对应的字体
    const availableFonts = [
      { size: 16, font: SANS_16_WHITE },
      { size: 32, font: SANS_32_WHITE },
      { size: 64, font: SANS_64_WHITE },
      { size: 128, font: SANS_128_WHITE }
    ];

    // 找到最接近目标大小的字体
    let bestFont = availableFonts[1]; // 默认32px
    let minDiff = Math.abs(targetSize - bestFont.size);

    for (const fontOption of availableFonts) {
      const diff = Math.abs(targetSize - fontOption.size);
      if (diff < minDiff) {
        minDiff = diff;
        bestFont = fontOption;
      }
    }

    const font = await loadFont(bestFont.font);
    return { font, actualFontSize: bestFont.size };
  }

  /**
   * 智能计算最佳字体大小
   */
  private static calculateOptimalFontSize(
    imageWidth: number,
    imageHeight: number,
    text: string,
    customFontSize?: number,
    fontSizeRatio?: number
  ): number {
    if (customFontSize) {
      return customFontSize;
    }

    // 使用自定义比例或默认比例 (5% = 0.05)
    const ratio = fontSizeRatio || 0.05;
    const baseSize = Math.floor(imageWidth * ratio);

    // 根据文字长度调整
    const textLength = text.length;
    let adjustedSize = baseSize;

    if (textLength > 15) {
      // 长文字适当缩小
      adjustedSize = Math.floor(baseSize * 0.8);
    } else if (textLength < 8) {
      // 短文字适当放大
      adjustedSize = Math.floor(baseSize * 1.2);
    }

    // 确保字体大小在合理范围内
    const minSize = Math.max(16, Math.floor(imageWidth / 50));
    const maxSize = Math.min(128, Math.floor(imageHeight / 8));

    return Math.max(minSize, Math.min(maxSize, adjustedSize));
  }

  /**
   * 计算文字尺寸
   */
  private static calculateTextDimensions(text: string, fontSize: number): { width: number; height: number } {
    // 更精确的文字宽度计算
    // 考虑不同字符的宽度差异
    let totalWidth = 0;
    for (const char of text) {
      if (/[\u4e00-\u9fff]/.test(char)) {
        // 中文字符
        totalWidth += fontSize * 0.9;
      } else if (/[A-Z]/.test(char)) {
        // 大写英文字母
        totalWidth += fontSize * 0.7;
      } else if (/[a-z]/.test(char)) {
        // 小写英文字母
        totalWidth += fontSize * 0.6;
      } else if (/[0-9]/.test(char)) {
        // 数字
        totalWidth += fontSize * 0.6;
      } else {
        // 其他字符（标点符号等）
        totalWidth += fontSize * 0.4;
      }
    }

    return {
      width: Math.ceil(totalWidth),
      height: Math.ceil(fontSize * 1.2)
    };
  }

  /**
   * 检测图片格式
   */
  private static detectImageFormat(buffer: Buffer): string {
    // 检查文件头来确定格式
    if (buffer[0] === 0xFF && buffer[1] === 0xD8) {
      return 'jpeg';
    } else if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
      return 'png';
    } else if (buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46) {
      return 'gif';
    } else if (buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46) {
      return 'webp';
    }

    // 默认返回 jpeg
    return 'jpeg';
  }

  /**
   * 获取优化后的图片 Buffer
   */
  private static async getOptimizedBuffer(image: Jimp, format: string): Promise<Buffer> {
    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        // JPEG 格式，使用 getBuffer 的 options 参数设置质量
        return await image.getBuffer('image/jpeg', { quality: 85 });

      case 'png':
        // PNG 格式，使用压缩
        return await image.getBuffer('image/png');

      case 'webp':
        // WebP 格式（如果支持）
        try {
          return await image.getBuffer('image/webp', { quality: 85 });
        } catch {
          // 如果不支持 WebP，回退到 JPEG
          return await image.getBuffer('image/jpeg', { quality: 85 });
        }

      default:
        // 默认使用 JPEG
        return await image.getBuffer('image/jpeg', { quality: 85 });
    }
  }

  /**
   * 计算水印位置
   */
  private static calculatePosition(
    position: WatermarkPosition,
    imageWidth: number,
    imageHeight: number,
    textWidth: number,
    textHeight: number,
    margin: number
  ): { x: number; y: number } {
    switch (position) {
      case WatermarkPosition.TOP_LEFT:
        return { x: margin, y: margin };

      case WatermarkPosition.TOP_RIGHT:
        return { x: imageWidth - textWidth - margin, y: margin };

      case WatermarkPosition.BOTTOM_LEFT:
        return { x: margin, y: imageHeight - textHeight - margin };

      case WatermarkPosition.BOTTOM_RIGHT:
        return { x: imageWidth - textWidth - margin, y: imageHeight - textHeight - margin };

      case WatermarkPosition.CENTER:
        return {
          x: Math.floor((imageWidth - textWidth) / 2),
          y: Math.floor((imageHeight - textHeight) / 2)
        };

      default:
        return { x: margin, y: imageHeight - textHeight - margin }; // 默认左下角
    }
  }

  /**
   * 从 URL 下载图片并添加水印
   * @param imageUrl 图片 URL
   * @param config 水印配置
   * @returns 添加水印后的图片 Buffer
   */
  static async addWatermarkFromUrl(
    imageUrl: string,
    config: WatermarkConfig
  ): Promise<Buffer> {
    try {
      console.log(`[Watermark Service] Downloading image from URL: ${imageUrl}`);
      
      // 下载图片
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
      }
      
      const imageBuffer = Buffer.from(await response.arrayBuffer());
      console.log(`[Watermark Service] Downloaded image, size: ${imageBuffer.length} bytes`);
      
      // 添加水印
      return await this.addWatermark(imageBuffer, config);
    } catch (error) {
      console.error(`[Watermark Service] Error downloading and watermarking image:`, error);
      throw error;
    }
  }

  /**
   * 获取默认水印配置
   */
  static getDefaultConfig(): WatermarkConfig {
    const webUrl = process.env.NEXT_PUBLIC_WEB_URL || 'kreaflux.org';

    return {
      text: webUrl,
      position: WatermarkPosition.BOTTOM_RIGHT,
      opacity: 0.7,
      fontSize: undefined, // 使用自动计算
      fontSizeRatio: 0.05, // 默认为图片宽度的5%
      color: '#FFFFFF',
      margin: 20
    };
  }
}
