// services/payment/creem.ts
import { PaymentProvider, CheckoutParams, PaymentSession, WebhookResult, SessionData } from './types';

export class CreemProvider implements PaymentProvider {
  private apiKey: string;
  private baseUrl: string;
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
    // 优先使用环境变量配置的API URL，否则根据密钥判断环境
    this.baseUrl = process.env.CREEM_API_URL ||
      (apiKey.includes('test')
        ? 'https://test-api.creem.io/v1'
        : 'https://api.creem.io/v1');
  }

  validateConfig(): boolean {
    return !!this.apiKey;
  }

  async createCheckoutSession(params: CheckoutParams): Promise<PaymentSession> {
    console.log('[CREEM] Creating checkout session with params:', {
      productId: params.productId,
      productName: params.productName,
      amount: params.amount,
      currency: params.currency,
      isSubscription: params.isSubscription,
      interval: params.interval,
      customerEmail: params.customerEmail,
      metadata: params.metadata
    });

    // 根据Creem文档，使用正确的API格式
    const requestBody: any = {
      product_id: params.productId, // 必须是字符串
      success_url: params.successUrl,
    };

    // 添加可选字段
    if (params.metadata && Object.keys(params.metadata).length > 0) {
      requestBody.metadata = params.metadata;
    }

    // 添加request_id用于追踪
    if (params.metadata?.order_no) {
      requestBody.request_id = params.metadata.order_no;
    }

    console.log('[CREEM] Using Creem-specific request format:', requestBody);

    const endpoint = `${this.baseUrl}/checkouts`;
    console.log('[CREEM] API Request Details:', {
      endpoint,
      method: 'POST',
      headers: {
        'x-api-key': this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT_SET',
        'Content-Type': 'application/json'
      },
      requestBody: JSON.stringify(requestBody, null, 2)
    });

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'x-api-key': this.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[CREEM] API Response Status:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    });

    if (!response.ok) {
      let errorDetails;
      try {
        errorDetails = await response.text();
        console.log('[CREEM] Error Response Body:', errorDetails);
      } catch (e) {
        console.log('[CREEM] Could not read error response body');
      }

      throw new Error(`Creem API error: ${response.status} ${response.statusText} - ${errorDetails || 'No error details'}`);
    }

    const data = await response.json();
    console.log('[CREEM] Success Response Data:', data);

    return {
      id: data.id,
      url: data.checkout_url, // Creem使用checkout_url字段
      provider: 'creem',
      metadata: { creemSessionId: data.id }
    };
  }

  async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
    try {
      const event = JSON.parse(body);
      console.log('[CREEM_WEBHOOK] Event type:', event.eventType);

      switch (event.eventType) {
        case 'checkout.completed':
          const hasSubscription = !!event.object.subscription;
          return {
            success: true,
            orderNo: event.object.metadata?.order_no,
            sessionId: event.object.id,
            eventType: 'checkout.completed',
            subscriptionData: hasSubscription ? event.object.subscription : null,
            isSubscription: hasSubscription,
            transactionId: event.object.order?.id || event.object.order?.transaction_id
          };

        case 'subscription.paid':
          return {
            success: true,
            subscriptionId: event.object.id,
            customerId: event.object.customer?.id,
            eventType: 'subscription.paid',
            subscriptionData: event.object,
            transactionId: event.object.last_transaction_id
          };

        case 'subscription.canceled':
          return {
            success: true,
            subscriptionId: event.object.id,
            eventType: 'subscription.canceled',
            subscriptionData: event.object,
            canceledAt: event.object.canceled_at
          };

        case 'subscription.expired':
          return {
            success: true,
            subscriptionId: event.object.id,
            eventType: 'subscription.expired',
            subscriptionData: event.object
          };

        case 'subscription.update':
          return {
            success: true,
            subscriptionId: event.object.id,
            eventType: 'subscription.update',
            subscriptionData: event.object
          };

        case 'subscription.trialing':
          return {
            success: true,
            subscriptionId: event.object.id,
            eventType: 'subscription.trialing',
            subscriptionData: event.object
          };

        case 'subscription.active':
          // 仅用于同步，不处理业务逻辑
          return {
            success: true,
            eventType: 'subscription.active'
          };

        default:
          return { success: true };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async retrieveSession(sessionId: string): Promise<SessionData> {
    console.log('[CREEM] Retrieving session:', sessionId);

    const endpoint = `${this.baseUrl}/checkout/${sessionId}`;
    console.log('[CREEM] Retrieve endpoint:', endpoint);

    const response = await fetch(endpoint, {
      headers: {
        'x-api-key': this.apiKey
      }
    });

    console.log('[CREEM] Retrieve response status:', response.status, response.statusText);

    if (!response.ok) {
      let errorDetails;
      try {
        errorDetails = await response.text();
        console.log('[CREEM] Retrieve error details:', errorDetails);
      } catch (e) {
        console.log('[CREEM] Could not read error response');
      }
      throw new Error(`Failed to retrieve Creem session: ${response.status} ${response.statusText} - ${errorDetails || 'No details'}`);
    }

    const session = await response.json();
    console.log('[CREEM] Retrieved session data:', session);

    return {
      id: session.id,
      paymentStatus: session.status,
      customerEmail: session.customer?.email || session.customer_email,
      metadata: session.metadata || {}
    };
  }

  private verifyWebhookSignature(body: string, signature: string): boolean {
    // 实现Creem的webhook签名验证
    // 具体实现需要参考Creem文档
    return true; // 临时返回true，需要实际实现
  }
}
