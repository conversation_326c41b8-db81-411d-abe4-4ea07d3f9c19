// services/payment/types.ts
export interface PaymentProvider {
  createCheckoutSession(params: CheckoutParams): Promise<PaymentSession>;
  handleWebhook(data: any, signature: string): Promise<WebhookResult>;
  retrieveSession(sessionId: string): Promise<SessionData>;
  validateConfig(): boolean;
}

export interface CheckoutParams {
  amount: number;
  currency: string;
  productName: string;
  productId: string;
  customerEmail?: string;
  successUrl: string;
  cancelUrl: string;
  metadata: Record<string, string>;
  isSubscription?: boolean;
  interval?: string;
}

export interface PaymentSession {
  id: string;
  url: string;
  provider: string;
  metadata?: Record<string, any>;
}

export interface WebhookResult {
  success: boolean;
  error?: string;
  orderNo?: string;
  sessionId?: string;
  eventType?: string;
  subscriptionId?: string;
  customerId?: string;
  subscriptionData?: any;
  isSubscription?: boolean; // 标识是否为订阅
  transactionId?: string; // 交易ID，用于去重
  canceledAt?: string; // 订阅取消时间
}

export interface SessionData {
  id: string;
  paymentStatus: string;
  customerEmail?: string;
  metadata: Record<string, any>;
}
