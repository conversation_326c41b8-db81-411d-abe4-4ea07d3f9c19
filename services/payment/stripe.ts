// services/payment/stripe.ts
import Strip<PERSON> from 'stripe';
import { PaymentProvider, CheckoutParams, PaymentSession, WebhookResult, SessionData } from './types';

export class StripeProvider implements PaymentProvider {
  private stripe: Stripe;
  
  constructor(privateKey: string) {
    this.stripe = new Stripe(privateKey);
  }

  validateConfig(): boolean {
    return !!(process.env.STRIPE_PRIVATE_KEY && process.env.STRIPE_PUBLIC_KEY);
  }

  async createCheckoutSession(params: CheckoutParams): Promise<PaymentSession> {
    const sessionOptions: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: params.currency,
          product_data: { name: params.productName },
          unit_amount: params.amount,
          recurring: params.isSubscription ? {
            interval: params.interval as Stripe.Price.Recurring.Interval
          } : undefined
        },
        quantity: 1
      }],
      mode: params.isSubscription ? 'subscription' : 'payment',
      success_url: params.successUrl,
      cancel_url: params.cancelUrl,
      customer_email: params.customerEmail,
      metadata: params.metadata
    };

    // 支持中国支付方式
    if (params.currency === 'cny') {
      sessionOptions.payment_method_types = ['wechat_pay', 'alipay', 'card'];
      sessionOptions.payment_method_options = {
        wechat_pay: { client: 'web' },
        alipay: {}
      };
    }

    const session = await this.stripe.checkout.sessions.create(sessionOptions);
    
    return {
      id: session.id,
      url: session.url!,
      provider: 'stripe',
      metadata: { stripeSessionId: session.id }
    };
  }

  async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
    try {
      const event = await this.stripe.webhooks.constructEventAsync(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      if (event.type === 'checkout.session.completed') {
        const session = event.data.object as Stripe.Checkout.Session;
        return {
          success: true,
          orderNo: session.metadata?.order_no,
          sessionId: session.id
        };
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async retrieveSession(sessionId: string): Promise<SessionData> {
    const session = await this.stripe.checkout.sessions.retrieve(sessionId);
    return {
      id: session.id,
      paymentStatus: session.payment_status,
      customerEmail: session.customer_details?.email,
      metadata: session.metadata || {}
    };
  }
}
