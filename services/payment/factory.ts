// services/payment/factory.ts
import { PaymentProvider } from './types';
import { StripeProvider } from './stripe';
import { CreemProvider } from './creem';

export class PaymentFactory {
  private static providers: Map<string, PaymentProvider> = new Map();

  static createProvider(provider: string): PaymentProvider {
    console.log('[PAYMENT_FACTORY] Creating provider:', provider);

    if (this.providers.has(provider)) {
      console.log('[PAYMENT_FACTORY] Using cached provider:', provider);
      return this.providers.get(provider)!;
    }

    let providerInstance: PaymentProvider;

    switch (provider) {
      case 'stripe':
        console.log('[PAYMENT_FACTORY] Creating Stripe provider with key:',
          process.env.STRIPE_PRIVATE_KEY ? `${process.env.STRIPE_PRIVATE_KEY.substring(0, 10)}...` : 'NOT_SET');
        providerInstance = new StripeProvider(process.env.STRIPE_PRIVATE_KEY!);
        break;
      case 'creem':
        console.log('[PAYMENT_FACTORY] Creating Creem provider with key:',
          process.env.CREEM_API_KEY ? `${process.env.CREEM_API_KEY.substring(0, 10)}...` : 'NOT_SET');
        providerInstance = new CreemProvider(process.env.CREEM_API_KEY!);
        break;
      default:
        console.error('[PAYMENT_FACTORY] Unsupported provider:', provider);
        throw new Error(`Unsupported payment provider: ${provider}`);
    }

    // 验证配置
    console.log('[PAYMENT_FACTORY] Validating provider config:', provider);
    if (!providerInstance.validateConfig()) {
      console.error('[PAYMENT_FACTORY] Invalid configuration for provider:', provider);
      throw new Error(`Invalid configuration for provider: ${provider}`);
    }

    console.log('[PAYMENT_FACTORY] Provider created and cached:', provider);
    this.providers.set(provider, providerInstance);
    return providerInstance;
  }

  static getEnabledProviders(): string[] {
    return process.env.PAYMENT_PROVIDERS_ENABLED?.split(',') || ['stripe'];
  }

  static getDefaultProvider(): string {
    return process.env.PAYMENT_DEFAULT_PROVIDER || 'stripe';
  }
}
