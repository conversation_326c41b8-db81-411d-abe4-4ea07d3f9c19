# Creem付款安全问题改进方案

## 🎯 核心原则
- 简洁有效，避免过度设计
- 优先解决严重安全漏洞
- 保持现有架构不变
- 最小化代码改动

## 🚨 新发现的严重漏洞

### 0. Credits数量完全信任前端输入
**位置**: `app/api/checkout/route.ts:19,129`
**问题**: 直接使用前端传来的credits值，用户可以伪造任意数量的积分
```typescript
let { credits } = requestData; // 直接信任前端
const order: Order = {
  credits: credits, // 未验证就存储
};
```

**风险**: 🔴 极严重 - 用户可以花1美元购买100亿积分
**修复**: 从pricing配置中获取正确的credits值

## 🔥 立即修复（高优先级）

### 0. 修复Credits验证漏洞

**修改文件**: `app/api/checkout/route.ts`

```typescript
export async function POST(req: Request) {
  try {
    const requestData = await req.json() as any;
    let {
      provider = PaymentFactory.getDefaultProvider(),
      // credits, // 删除这行，不再信任前端
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
    } = requestData;

    // ... 其他验证逻辑保持不变 ...

    // 从pricing配置中获取正确的credits值
    const item = page.pricing.items.find(
      (item: PricingItem) => item.product_id === product_id
    );
    if (
      !item ||
      !item.amount ||
      !item.interval ||
      !item.currency ||
      item.amount !== amount ||
      item.interval !== interval ||
      item.currency !== currency
    ) {
      return respErr("invalid checkout params");
    }

    // 使用配置中的credits值，而不是前端传来的
    const credits = item.credits || 0;

    console.log('[CHECKOUT] Using credits from config:', {
      productId: product_id,
      configuredCredits: credits,
      // 不再记录前端传来的credits
    });

    const order: Order = {
      order_no: order_no,
      created_at: created_at,
      user_uuid: user_uuid,
      user_email: user_email,
      amount: amount,
      interval: interval,
      expired_at: expired_at,
      status: "created",
      credits: credits, // 使用验证过的credits
      currency: currency,
      product_id: product_id,
      product_name: product_name,
      valid_months: valid_months,
      payment_provider: provider,
    };

    // ... 其余逻辑保持不变 ...
  } catch (e: any) {
    console.log("checkout failed: ", e);
    return respErr("checkout failed: " + e.message);
  }
}
```

### 1. 实现Webhook签名验证

**修改文件**: `services/payment/creem.ts`

```typescript
private verifyWebhookSignature(body: string, signature: string): boolean {
  try {
    const webhookSecret = process.env.CREEM_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('[CREEM] Webhook secret not configured');
      return false;
    }

    // 根据Creem文档，使用HMAC-SHA256验证
    const crypto = require('crypto');
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex');

    // 安全比较，防止时序攻击
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    console.error('[CREEM] Signature verification failed:', error);
    return false;
  }
}
```

### 2. 启用签名验证检查

**修改文件**: `services/payment/creem.ts`

```typescript
async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
  try {
    console.log('[CREEM_WEBHOOK] Processing webhook');

    const event = JSON.parse(body);

    // 启用签名验证
    if (!this.verifyWebhookSignature(body, signature)) {
      console.error('[CREEM_WEBHOOK] Invalid signature');
      return { success: false, error: 'Invalid signature' };
    }

    console.log('[CREEM_WEBHOOK] Signature verified, event type:', event.eventType);

    if (event.eventType === 'checkout.completed') {
      const result = {
        success: true,
        orderNo: event.object.metadata?.order_no,
        sessionId: event.object.id
      };
      console.log('[CREEM_WEBHOOK] Checkout completed:', result.orderNo);
      return result;
    }

    return { success: true };
  } catch (error) {
    console.error('[CREEM_WEBHOOK] Error processing webhook:', error);
    return {
      success: false,
      error: 'Webhook processing failed'
    };
  }
}
```

### 3. 修正签名头名称

**修改文件**: `app/api/payment-notify/[provider]/route.ts`

```typescript
const signature = req.headers.get(`${provider}-signature`) ||
                 req.headers.get('stripe-signature') ||
                 req.headers.get('creem-signature') || ''; // 修正为 creem-signature
```

## 🔧 短期修复（中优先级）

### 4. 添加简单的二次验证

**修改文件**: `services/order.ts`

在 `handlePaymentCallback` 函数中添加验证：

```typescript
export async function handlePaymentCallback(
  provider: string, 
  sessionId: string, 
  orderNo: string
) {
  try {
    console.log(`[PAYMENT_CALLBACK] Processing ${provider} callback:`, { sessionId, orderNo });

    // 查找订单
    const order = await findOrderByOrderNo(orderNo);
    if (!order) {
      console.error(`[PAYMENT_CALLBACK] Order not found:`, { sessionId, orderNo, provider });
      throw new Error("Order not found");
    }

    if (order.status !== "created") {
      console.warn(`[PAYMENT_CALLBACK] Order already processed:`, {
        orderNo: order.order_no,
        currentStatus: order.status
      });
      if (order.status === "paid") {
        console.log(`[PAYMENT_CALLBACK] Order already paid, skipping processing`);
        return;
      }
      throw new Error(`Order status is ${order.status}, expected 'created'`);
    }

    // 简单的二次验证：验证sessionId是否匹配
    if (order.payment_session_id && order.payment_session_id !== sessionId) {
      console.error(`[PAYMENT_CALLBACK] Session ID mismatch:`, {
        orderSessionId: order.payment_session_id,
        webhookSessionId: sessionId
      });
      throw new Error("Session ID mismatch");
    }

    // 继续原有处理逻辑...
    const paid_at = getIsoTimestr();
    const paid_email = order.user_email;
    const paid_detail = JSON.stringify({
      provider,
      sessionId,
      orderNo,
      processedVia: 'webhook',
      timestamp: paid_at
    });

    await updateOrderStatus(order.order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.credits > 0) {
      console.log(`[PAYMENT_CALLBACK] Updating credits:`, order.credits);
      await updateCreditForOrder(order);
    }

    if (order.user_uuid) {
      console.log(`[PAYMENT_CALLBACK] Updating affiliate`);
      await updateAffiliateForOrder(order);
    }

    console.log(`[PAYMENT_CALLBACK] Order ${order.order_no} processed successfully via ${provider}`);
  } catch (error) {
    console.error(`[PAYMENT_CALLBACK] Failed to process ${provider} payment callback:`, error);
    throw error;
  }
}
```

### 5. 优化日志处理

**修改文件**: `services/payment/creem.ts`

```typescript
// 添加日志脱敏函数
private sanitizeLogData(data: any): any {
  if (typeof data === 'string') {
    try {
      data = JSON.parse(data);
    } catch {
      return '[SANITIZED_STRING]';
    }
  }

  if (typeof data === 'object' && data !== null) {
    const sanitized = { ...data };
    
    // 脱敏敏感字段
    const sensitiveFields = ['email', 'customer_email', 'phone', 'address'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[SANITIZED]';
      }
    });

    // 递归处理嵌套对象
    Object.keys(sanitized).forEach(key => {
      if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeLogData(sanitized[key]);
      }
    });

    return sanitized;
  }

  return data;
}

// 在handleWebhook中使用
async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
  try {
    const event = JSON.parse(body);
    
    // 使用脱敏日志
    console.log('[CREEM_WEBHOOK] Processing webhook:', this.sanitizeLogData(event));
    
    // ... 其他逻辑
  } catch (error) {
    console.error('[CREEM_WEBHOOK] Error processing webhook:', error.message);
    return {
      success: false,
      error: 'Webhook processing failed'
    };
  }
}
```

## 🔍 长期改进（低优先级）

### 6. 加强配置验证

**修改文件**: `services/payment/creem.ts`

```typescript
validateConfig(): boolean {
  const requiredEnvVars = [
    'CREEM_API_KEY',
    'CREEM_WEBHOOK_SECRET'
  ];

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    console.error('[CREEM] Missing required environment variables:', missing);
    return false;
  }

  // 简单的API密钥格式验证
  const apiKey = process.env.CREEM_API_KEY!;
  if (!apiKey.startsWith('creem_')) {
    console.warn('[CREEM] API key format may be incorrect');
  }

  return !!this.apiKey;
}
```

### 7. 改进错误处理

**修改文件**: `app/api/payment-notify/[provider]/route.ts`

```typescript
export async function POST(
  req: Request,
  { params }: { params: Promise<{ provider: string }> }
) {
  try {
    const { provider } = await params;

    const enabledProviders = PaymentFactory.getEnabledProviders();
    if (!enabledProviders.includes(provider)) {
      return respErr(`Unsupported payment provider`); // 不暴露具体provider
    }

    const body = await req.text();
    const signature = req.headers.get(`${provider}-signature`) ||
                     req.headers.get('stripe-signature') ||
                     req.headers.get('creem-signature') || '';

    if (!signature || !body) {
      return respErr("Invalid request");
    }

    const paymentProvider = PaymentFactory.createProvider(provider);
    const result = await paymentProvider.handleWebhook(body, signature);

    if (result.success && result.orderNo && result.sessionId) {
      await handlePaymentCallback(provider, result.sessionId, result.orderNo);
    }

    return respOk();
  } catch (e: any) {
    const { provider: providerName } = await params;
    console.error(`${providerName} webhook failed:`, e.message); // 只记录错误消息
    return respErr(`Webhook processing failed`); // 通用错误信息
  }
}
```

## 📝 实施步骤

### 第0步：紧急修复Credits漏洞（15分钟）
1. 修改checkout路由，从配置获取credits
2. 删除对前端credits的信任
3. 立即部署此修复

### 第1步：立即修复签名验证（30分钟）
1. 修改 `verifyWebhookSignature` 方法
2. 启用 `handleWebhook` 中的签名验证
3. 修正签名头名称

### 第2步：添加基础验证（1小时）
1. 在订单处理中添加sessionId验证
2. 优化日志脱敏处理

### 第3步：完善配置和错误处理（1小时）
1. 加强环境变量验证
2. 改进错误处理和响应

## ✅ 验证方法

1. **Credits验证**：尝试修改前端请求中的credits值，确认后端使用配置值
2. **本地测试**：使用Creem测试环境验证签名
3. **日志检查**：确认敏感信息已脱敏
4. **错误测试**：验证错误处理不泄露信息
5. **配置检查**：确认所有环境变量正确配置

## 🎯 预期效果

- ✅ 消除极严重的Credits漏洞
- ✅ 消除严重的签名验证漏洞
- ✅ 保持代码简洁性
- ✅ 最小化业务影响
- ✅ 提升整体安全性

## ⚠️ 紧急提醒

**Credits漏洞是最严重的财务安全问题，建议立即修复并部署！**
用户可以通过修改前端请求轻松获得任意数量的积分，这可能导致重大财务损失。
