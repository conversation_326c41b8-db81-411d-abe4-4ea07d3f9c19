import { getRequestConfig } from "next-intl/server";
import { routing } from "./routing";

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale;
  if (!locale || !routing.locales.includes(locale as any)) {
    locale = routing.defaultLocale;
  }

  if (["zh-CN"].includes(locale)) {
    locale = "zh";
  }

  if (!routing.locales.includes(locale as any)) {
    locale = "en";
  }

  try {
    const messages = (await import(`./messages/${locale.toLowerCase()}.json`))
      .default;

    // 加载页面特定的翻译文件
    const pageMessages: Record<string, any> = {};

    // 加载ai-dashboard翻译
    try {
      const aiDashboardMessages = (await import(`./pages/ai-dashboard/${locale.toLowerCase()}.json`)).default;
      pageMessages['ai-dashboard'] = aiDashboardMessages;
    } catch (e) {
      // 如果加载失败，使用英文作为后备
      try {
        const aiDashboardMessages = (await import(`./pages/ai-dashboard/en.json`)).default;
        pageMessages['ai-dashboard'] = aiDashboardMessages;
      } catch (e) {
        // 忽略错误，继续执行
      }
    }

    // 加载AiModelTranslation翻译
    try {
      const aiModelTranslationMessages = (await import(`./pages/AiModelTranslation/${locale.toLowerCase()}.json`)).default;
      pageMessages['AiModelTranslation'] = aiModelTranslationMessages;
    } catch (e) {
      // 如果加载失败，使用英文作为后备
      try {
        const aiModelTranslationMessages = (await import(`./pages/AiModelTranslation/en.json`)).default;
        pageMessages['AiModelTranslation'] = aiModelTranslationMessages;
      } catch (e) {
        // 忽略错误，继续执行
      }
    }

    return {
      locale: locale,
      messages: {
        ...messages,
        ...pageMessages
      },
    };
  } catch (e) {
    return {
      locale: "en",
      messages: (await import(`./messages/en.json`)).default,
    };
  }
});
