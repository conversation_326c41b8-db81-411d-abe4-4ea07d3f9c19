{"common": {"parameters": {"max_tokens": {"description": "Maximum output length", "tooltip": "Maximum number of tokens to generate, affects response length"}, "temperature": {"description": "Creativity level", "tooltip": "Controls output randomness, 0 is most conservative, 1 is most creative"}, "top_p": {"description": "Nucleus sampling", "tooltip": "Controls vocabulary selection range, smaller values make output more focused"}, "stream": {"description": "Streaming output", "tooltip": "When enabled, displays generation process in real-time"}, "aspectRatio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "cdn": {"description": "CDN selection", "tooltip": "Choose CDN node for storage and access"}, "uploadedImages": {"description": "Reference images", "tooltip": "Upload reference images, supports multiple images"}, "variants": {"description": "Generation count", "tooltip": "Select the number of images to generate"}, "size": {"description": "Image size", "tooltip": "Select the specific dimensions for generated images"}, "vision_detail": {"description": "Image analysis detail", "tooltip": "Controls the level of detail in image analysis", "options": {"auto": {"label": "Auto", "description": "Automatically select the best image processing method"}, "low": {"label": "Low detail", "description": "Fast processing, suitable for simple images"}, "high": {"label": "High detail", "description": "Detailed analysis, suitable for complex images"}}}, "duration": {"description": "Video duration", "tooltip": "Select the duration of the generated video"}, "resolution": {"description": "Video resolution", "tooltip": "Select the resolution of the generated video"}, "fps": {"description": "Frame rate", "tooltip": "Select the frame rate of the video"}, "quality": {"description": "Generation quality", "tooltip": "Select the quality level for generation"}, "motion_intensity": {"description": "Motion intensity", "tooltip": "Control the motion amplitude, 1 for static, 10 for intense motion"}}, "parameterGroups": {"basic": "Basic Settings", "advanced": "Advanced Settings", "expert": "Expert Settings"}, "options": {"aspectRatio": {"1:1": {"label": "Square (1:1)", "description": "Perfect for social media avatars"}, "16:9": {"label": "Landscape (16:9)", "description": "Perfect for video covers"}, "9:16": {"label": "Portrait (9:16)", "description": "Perfect for mobile wallpapers"}, "4:3": {"label": "Standard (4:3)", "description": "Classic photo ratio"}, "3:2": {"label": "Photo (3:2)", "description": "Camera default ratio"}, "21:9": {"label": "Ultra-wide (21:9)", "description": "Cinematic ratio"}, "9:21": {"label": "Ultra-tall (9:21)", "description": "Ultra-tall portrait ratio"}, "2:3": {"label": "Portrait Photo (2:3)", "description": "Portrait camera ratio"}, "4:5": {"label": "Instagram (4:5)", "description": "Instagram portrait"}, "5:4": {"label": "Classic Landscape (5:4)", "description": "Classic landscape ratio"}, "3:4": {"label": "Standard Portrait (3:4)", "description": "Standard portrait ratio"}}, "cdn": {"global": {"label": "Global CDN", "description": "Use global CDN acceleration"}, "zh": {"label": "China CDN", "description": "Use China CDN acceleration"}}, "size": {"1024x1024": {"label": "Square (1024x1024)", "description": "Standard square size"}, "1792x1024": {"label": "Landscape (1792x1024)", "description": "Widescreen size"}, "1024x1792": {"label": "Portrait (1024x1792)", "description": "Portrait size"}}, "duration": {"5": {"label": "5 seconds", "description": "Short video, quick generation"}, "10": {"label": "10 seconds", "description": "Medium length video"}, "15": {"label": "15 seconds", "description": "Long video, richer content"}}, "resolution": {"720p": {"label": "720p (HD)", "description": "HD resolution, balanced quality and speed"}, "1080p": {"label": "1080p (Full HD)", "description": "Full HD resolution, higher quality"}, "4k": {"label": "4K (Ultra HD)", "description": "Ultra HD resolution, highest quality"}}, "fps": {"24": {"label": "24 FPS", "description": "Cinematic frame rate"}, "30": {"label": "30 FPS", "description": "Standard video frame rate"}, "60": {"label": "60 FPS", "description": "High frame rate, smoother motion"}}, "quality": {"standard": {"label": "Standard Quality", "description": "Balanced quality and speed"}, "high": {"label": "High Quality", "description": "Better visual effects"}, "ultra": {"label": "Ultra Quality", "description": "Best visual effects, slower generation"}}, "goFast": {"true": {"label": "Enable Fast Mode", "description": "Use fp8 quantization, faster speed"}, "false": {"label": "Disable Fast Mode", "description": "Use bf16 precision, more deterministic results"}}, "safetyChecker": {"false": {"label": "Enable Safety Check", "description": "Enable content safety checking"}, "true": {"label": "Disable Safety Check", "description": "Disable content safety checking"}}, "variants": {"1": {"label": "1 image", "description": "Generate 1 image"}, "2": {"label": "2 images", "description": "Generate 2 images"}, "3": {"label": "3 images", "description": "Generate 3 images"}, "4": {"label": "4 images", "description": "Generate 4 images"}}, "outputFormat": {"webp": {"label": "WebP", "description": "Modern format, high compression"}, "jpg": {"label": "JPG", "description": "Universal format, good compatibility"}, "png": {"label": "PNG", "description": "Lossless compression, larger file size"}}, "megapixels": {"0_25": {"label": "0.25 megapixels", "description": "Fast generation, lower resolution"}, "1": {"label": "1 megapixel", "description": "Standard quality, balanced speed"}}, "boolean": {"true": {"label": "Enable", "description": "Turn on this feature"}, "false": {"label": "Disable", "description": "Turn off this feature"}}}, "ui": {"range": "Range", "minValue": "Min value", "maxValue": "Max value", "step": "Step"}}, "models": {"grsai": {"gemini-2-5-pro": {"name": "Gemini 2.5 Pro", "description": "Advanced conversational model, suitable for complex tasks and professional use"}, "gemini-2-5-flash": {"name": "Gemini 2.5 Flash", "description": "Fast conversational model, quick response and high efficiency"}, "gemini-2-5-flash-lite": {"name": "Gemini 2.5 Flash Lite", "description": "Lightweight conversational model, low cost and basic functionality"}, "gpt-4o-mini": {"name": "GPT-4o Mini", "description": "GPT-4o lightweight version, balanced performance and cost"}, "o4-mini-all": {"name": "GPT-4o Mini All", "description": "GPT-4o Mini full-featured version with vision and multimodal capabilities", "parameters": {"uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}}}, "gpt-4o-all": {"name": "GPT-4o All", "description": "GPT-4o complete version with all advanced features and multimodal support", "parameters": {"uploadedImages": {"description": "Image input", "tooltip": "Upload images for visual analysis and understanding"}}}, "flux-pro-1-1": {"name": "Flux Pro 1.1", "description": "Professional image generation with Flux v1.1 technology"}, "gpt-4o-image": {"name": "GPT-4o Image", "description": "High-quality image generation using GPT-4o architecture"}, "flux-kontext-pro": {"name": "Flux Kontext Pro", "description": "Context-aware professional-grade image generation"}, "sora-image": {"name": "Sora Image", "description": "Advanced image generation model based on Sora technology"}, "flux-pro-1-1-ultra": {"name": "Flux Pro 1.1 Ultra", "description": "Enhanced Flux Pro ultra-high quality image generation"}, "flux-kontext-max": {"name": "Flux Kontext Max", "description": "Highest quality context-aware image generation"}, "veo3-fast": {"name": "Veo3 Fast", "description": "Fast video generation with Veo3 technology, quick results", "parameters": {"uploadedImages": {"description": "First frame image", "tooltip": "Upload first frame image as the starting frame of the video"}}}, "veo3-pro": {"name": "Veo3 Pro", "description": "Professional video generation with advanced Veo3 capabilities", "parameters": {"uploadedImages": {"description": "First frame image", "tooltip": "Upload first frame image as the starting frame of the video"}}}}, "replicate": {"flux-krea-dev": {"name": "Flux Krea Dev", "description": "An opinionated text-to-image model from Black Forest Labs in collaboration with K<PERSON> that excels in photorealism. Creates images that avoid the oversaturated AI look.", "parameters": {"aspect_ratio": {"description": "Image aspect ratio", "tooltip": "Select the aspect ratio for generated images"}, "num_outputs": {"description": "Number of outputs", "tooltip": "Select the number of images to generate"}, "output_format": {"description": "Output format", "tooltip": "Select the file format for generated images"}, "output_quality": {"description": "Output quality", "tooltip": "Image quality, 0-100, 100 is highest quality (not applicable to PNG)"}, "guidance": {"description": "Guidance strength", "tooltip": "Controls how closely the generated image follows the prompt, higher values are stricter"}, "num_inference_steps": {"description": "Inference steps", "tooltip": "Number of denoising steps, recommended 28-50, more steps improve quality but slower"}, "seed": {"description": "Random seed", "tooltip": "Set random seed for reproducible results, leave empty for random"}, "prompt_strength": {"description": "Prompt strength", "tooltip": "Prompt strength when using image input, 1.0 means complete regeneration"}, "megapixels": {"description": "Image size", "tooltip": "Controls the resolution of generated images"}, "go_fast": {"description": "Fast mode", "tooltip": "Enable fast mode to accelerate generation, but may affect quality"}, "disable_safety_checker": {"description": "Safety checker", "tooltip": "Whether to disable safety checking for generated images"}}}}}}