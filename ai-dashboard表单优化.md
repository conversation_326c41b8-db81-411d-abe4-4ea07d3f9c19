# AI Dashboard 表单重构方案 v3.0：React Hook Form + Zod + Reducer

## 版本历史
- **v1.0**: 过度设计版本 - 新增多个自定义 hook 和独立目录（被否决）
- **v2.0**: 单文件版本 - 所有逻辑写入 useAIGeneration.ts（文件过长）
- **v3.0**: 当前版本 - Reducer 独立 + Hook 重构（推荐）

## 当前架构分析

### 现有问题
1. **状态分散**：表单状态分布在多个组件中，难以统一管理
2. **数据流复杂**：父子组件间通过多层 props 传递状态
3. **验证缺失**：缺乏统一的表单验证机制
4. **状态同步困难**：output 组件需要的信息散落在各处

## 重构目标

### 核心目标
1. **统一状态管理**：使用 React Hook Form 管理所有表单状态
2. **类型安全**：使用 Zod 进行运行时验证
3. **状态共享**：使用 Context 在 input 和 output 间共享状态
4. **最小化文件变更**：尽量复用现有文件结构
5. **不要考虑向后兼容**：直接实现最优解决方案，减少因为向后兼容增加的复杂度

## 技术方案（简化版）

### 1. 在现有 types.ts 中新增类型定义

```typescript
// components/ai-dashboard/components/types.ts (新增内容)
import { z } from 'zod'

// Zod Schema 定义
export const aiFormSchema = z.object({
  modelType: z.enum(['text', 'image', 'video']).default('text'),
  modelId: z.string().min(1, '请选择模型'),
  prompt: z.string().min(1, '请输入提示词').max(2000, '提示词过长'),
  options: z.object({
    size: z.string().optional(),
    aspectRatio: z.string().optional(),
    variants: z.number().min(1).max(4).default(1),
    temperature: z.number().min(0).max(1).default(0.7),
    max_tokens: z.number().min(1).max(4000).default(1000),
    cdn: z.string().default('global'),
    uploadedImages: z.array(z.string()).default([]),
    referenceImages: z.array(z.string()).default([]),
    // Replicate 特定选项
    output_quality: z.number().min(0).max(100).optional(),
    output_format: z.enum(['webp', 'jpg', 'png']).optional(),
    guidance: z.number().min(0).max(10).optional(),
    num_inference_steps: z.number().min(1).max(50).optional(),
    prompt_strength: z.number().min(0).max(1).optional(),
    disable_safety_checker: z.boolean().optional(),
    go_fast: z.boolean().optional(),
    megapixels: z.enum(['0.25', '1']).optional(),
    seed: z.number().optional(),
  }).default({})
})

export type AIFormData = z.infer<typeof aiFormSchema>

// 扩展现有的 GenerationResult 接口
export interface EnhancedGenerationResult extends GenerationResult {
  formData?: AIFormData
  selectedModel?: AIModel
  timestamp?: Date
  creditsUsed?: number
}

// 全局状态接口
export interface AIGenerationState {
  // 表单数据
  formData: AIFormData
  
  // 现有状态（保持兼容）
  selectedModel: AIModel | null
  models: AIModel[]
  modelsLoading: boolean
  modelsError: string | null
  prompt: string
  options: GenerationOptions
  loading: boolean
  costEstimate: CostEstimate | null
  userCredits: number
  
  // 新增状态
  generationResult: GenerationResult | null
  generationHistory: EnhancedGenerationResult[]
}
```

### 2. 新增 Reducer 文件

```typescript
// components/ai-dashboard/components/ai-generation.reducer.ts (新建)
import type { AIModel, GenerationResult, CostEstimate, AIFormData, EnhancedGenerationResult } from "./types";

export interface AIGenerationState {
  // 表单数据
  formData: AIFormData
  
  // 模型相关
  selectedModel: AIModel | null
  models: AIModel[]
  modelsLoading: boolean
  modelsError: string | null
  
  // 生成状态
  loading: boolean
  generationResult: GenerationResult | null
  generationHistory: EnhancedGenerationResult[]
  
  // 成本和积分
  costEstimate: CostEstimate | null
  userCredits: number
}

export type AIGenerationAction = 
  | { type: 'SET_FORM_DATA'; payload: Partial<AIFormData> }
  | { type: 'SET_SELECTED_MODEL'; payload: AIModel | null }
  | { type: 'SET_MODELS'; payload: { models: AIModel[]; loading: boolean; error: string | null } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_GENERATION_RESULT'; payload: GenerationResult | null }
  | { type: 'ADD_TO_HISTORY'; payload: EnhancedGenerationResult }
  | { type: 'SET_COST_ESTIMATE'; payload: CostEstimate | null }
  | { type: 'SET_USER_CREDITS'; payload: number }

export const initialState: AIGenerationState = {
  formData: {
    modelType: 'text',
    modelId: '',
    prompt: '',
    options: {}
  },
  selectedModel: null,
  models: [],
  modelsLoading: false,
  modelsError: null,
  loading: false,
  generationResult: null,
  generationHistory: [],
  costEstimate: null,
  userCredits: 0
}

export function aiGenerationReducer(
  state: AIGenerationState, 
  action: AIGenerationAction
): AIGenerationState {
  switch (action.type) {
    case 'SET_FORM_DATA':
      return {
        ...state,
        formData: { ...state.formData, ...action.payload }
      }
    
    case 'SET_SELECTED_MODEL':
      return {
        ...state,
        selectedModel: action.payload,
        formData: {
          ...state.formData,
          modelId: action.payload?.model_id || ''
        }
      }
    
    case 'SET_MODELS':
      return {
        ...state,
        models: action.payload.models,
        modelsLoading: action.payload.loading,
        modelsError: action.payload.error
      }
    
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      }
    
    case 'SET_GENERATION_RESULT':
      return {
        ...state,
        generationResult: action.payload
      }
    
    case 'ADD_TO_HISTORY':
      return {
        ...state,
        generationHistory: [action.payload, ...state.generationHistory.slice(0, 9)] // 保留最近10条
      }
    
    case 'SET_COST_ESTIMATE':
      return {
        ...state,
        costEstimate: action.payload
      }
    
    case 'SET_USER_CREDITS':
      return {
        ...state,
        userCredits: action.payload
      }
    
    default:
      return state
  }
}
```

### 3. 重构 useAIGeneration Hook

```typescript
// components/ai-dashboard/components/hooks/useAIGeneration.ts (重构)
"use client";

import { useReducer, useEffect, useContext, createContext, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useTranslations, useLocale } from "next-intl";
import { useModalStore } from "@/stores/modalStore";
import { getLocalActiveAIModels } from "@/services/provider/model-manager";
import { getAiModelTranslation } from "@/services/page";
import type { AIFormData, aiFormSchema, EnhancedGenerationResult } from "../types";
import { aiGenerationReducer, initialState, type AIGenerationAction, type AIGenerationState } from "../ai-generation.reducer";

// Context 定义
interface AIGenerationContextType {
  // 状态
  state: AIGenerationState
  dispatch: React.Dispatch<AIGenerationAction>
  
  // React Hook Form 实例
  form: ReturnType<typeof useForm<AIFormData>>
  
  // 方法
  handleGenerate: () => Promise<void>
  handleModelSelect: (modelId: string) => void
  fetchUserCredits: () => Promise<void>
  loadModels: () => Promise<void>
}

const AIGenerationContext = createContext<AIGenerationContextType | null>(null)

// Provider 组件
export function AIGenerationProvider({ 
  children, 
  modelType 
}: { 
  children: React.ReactNode
  modelType?: string 
}) {
  const t = useTranslations("ai-dashboard")
  const locale = useLocale()
  const { openModal } = useModalStore()
  
  // Reducer 状态管理
  const [state, dispatch] = useReducer(aiGenerationReducer, {
    ...initialState,
    formData: {
      ...initialState.formData,
      modelType: (modelType as any) || 'text'
    }
  })
  
  // React Hook Form 设置
  const form = useForm<AIFormData>({
    resolver: zodResolver(aiFormSchema),
    defaultValues: state.formData
  })
  
  // 监听表单变化，同步到 reducer
  const watchedValues = form.watch()
  useEffect(() => {
    dispatch({ type: 'SET_FORM_DATA', payload: watchedValues })
  }, [watchedValues])
  
  // 初始化
  useEffect(() => {
    fetchUserCredits()
    loadModels()
  }, [locale])
  
  // 模型类型变化时重新筛选
  useEffect(() => {
    if (modelType && state.selectedModel?.model_type !== modelType) {
      const filteredModels = state.models.filter(m => m.model_type === modelType)
      if (filteredModels.length > 0 && !filteredModels.find(m => m.model_id === state.selectedModel?.model_id)) {
        const newModel = filteredModels[0]
        dispatch({ type: 'SET_SELECTED_MODEL', payload: newModel })
        form.setValue('modelId', newModel.model_id)
      }
    }
  }, [modelType, state.models, state.selectedModel])
  
  // 成本估算
  useEffect(() => {
    if (state.selectedModel && state.formData.prompt) {
      estimateCost()
    }
  }, [state.selectedModel, state.formData.prompt, state.formData.options])
  
  // 业务逻辑方法
  const fetchUserCredits = useCallback(async () => {
    try {
      const response = await fetch('/api/get-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await response.json()
      if (data.code === 0) {
        dispatch({ type: 'SET_USER_CREDITS', payload: data.data.credits?.left_credits || 0 })
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error)
    }
  }, [])
  
  const loadModels = useCallback(async () => {
    try {
      dispatch({ type: 'SET_MODELS', payload: { models: [], loading: true, error: null } })
      
      const localModels = getLocalActiveAIModels()
      const translations = await getAiModelTranslation(locale)
      
      // 应用翻译
      const translatedModels = localModels.map(model => {
        if ((model.provider === 'grsai' || model.provider === 'replicate') && model.translationKey) {
          const [provider, ...modelIdParts] = model.translationKey.split('.')
          const normalizedModelId = modelIdParts.join('.').replace(/\./g, '-')
          const modelTranslation = translations.models[provider]?.[normalizedModelId]
          
          if (modelTranslation) {
            return {
              ...model,
              model_name: modelTranslation.name || model.model_name,
              description: modelTranslation.description || model.description
            }
          }
        }
        return model
      })
      
      dispatch({ type: 'SET_MODELS', payload: { models: translatedModels as any, loading: false, error: null } })
      
      // 自动选择第一个模型
      const filteredModels = modelType 
        ? translatedModels.filter(m => m.model_type === modelType)
        : translatedModels
      
      if (filteredModels.length > 0 && !state.selectedModel) {
        const firstModel = filteredModels[0]
        dispatch({ type: 'SET_SELECTED_MODEL', payload: firstModel as any })
        form.setValue('modelId', firstModel.model_id)
      }
    } catch (error) {
      dispatch({ type: 'SET_MODELS', payload: { models: [], loading: false, error: 'Failed to load models' } })
    }
  }, [locale, modelType])
  
  const handleModelSelect = useCallback((modelId: string) => {
    const model = state.models.find(m => m.model_id === modelId)
    if (model) {
      dispatch({ type: 'SET_SELECTED_MODEL', payload: model })
      form.setValue('modelId', modelId)
    }
  }, [state.models, form])
  
  const estimateCost = useCallback(async () => {
    if (!state.selectedModel || !state.formData.prompt) return
    
    try {
      const response = await fetch('/api/ai/estimate-cost', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: state.selectedModel.model_id,
          type: state.selectedModel.model_type,
          prompt: state.formData.prompt,
          options: state.formData.options
        })
      })
      const data = await response.json()
      if (data.code === 0) {
        dispatch({ type: 'SET_COST_ESTIMATE', payload: data.data })
      }
    } catch (error) {
      console.error('Failed to estimate cost:', error)
    }
  }, [state.selectedModel, state.formData])
  
  const handleGenerate = useCallback(async () => {
    // 表单验证
    const isValid = await form.trigger()
    if (!isValid) {
      toast.error(t("errors.invalid_input"))
      return
    }
    
    if (!state.selectedModel || !state.formData.prompt.trim()) {
      toast.error(t("errors.invalid_input"))
      return
    }

    if (state.costEstimate && !state.costEstimate.user_credits.can_afford) {
      toast.error(t("errors.insufficient_credits"))
      return
    }

    dispatch({ type: 'SET_LOADING', payload: true })
    dispatch({ type: 'SET_GENERATION_RESULT', payload: null })

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: state.formData.modelId,
          type: state.formData.modelType,
          prompt: state.formData.prompt,
          options: state.formData.options
        })
      })

      const data = await response.json()

      if (data.code === 0) {
        const result = data.data
        dispatch({ type: 'SET_GENERATION_RESULT', payload: result })
        
        if (result.status === 'success') {
          // 添加到历史记录
          const enhancedResult: EnhancedGenerationResult = {
            ...result,
            formData: state.formData,
            selectedModel: state.selectedModel,
            timestamp: new Date(),
            creditsUsed: result.usage?.credits_consumed
          }
          dispatch({ type: 'ADD_TO_HISTORY', payload: enhancedResult })
          toast.success(t("status.success"))
          fetchUserCredits()
        } else if (result.status === 'pending' || result.status === 'running') {
          // 轮询逻辑
          pollResult(result.task_id)
        }
      } else if (data.code === -2) {
        openModal('login', {})
      } else {
        toast.error(data.msg || t("errors.generation_failed"))
      }
    } catch (error) {
      toast.error(t("errors.network_error"))
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [form, state, t, openModal, fetchUserCredits])
  
  const pollResult = useCallback(async (taskId: string) => {
    const maxAttempts = 60
    let attempts = 0

    const poll = async () => {
      try {
        const response = await fetch('/api/ai/result', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ task_id: taskId })
        })

        const data = await response.json()

        if (data.code === 0) {
          dispatch({ type: 'SET_GENERATION_RESULT', payload: data.data })

          if (data.data.status === 'success') {
            const enhancedResult: EnhancedGenerationResult = {
              ...data.data,
              formData: state.formData,
              selectedModel: state.selectedModel,
              timestamp: new Date(),
              creditsUsed: data.data.usage?.credits_consumed
            }
            dispatch({ type: 'ADD_TO_HISTORY', payload: enhancedResult })
            toast.success(t("status.success"))
            fetchUserCredits()
            dispatch({ type: 'SET_LOADING', payload: false })
            return
          } else if (data.data.status === 'failed') {
            toast.error(t("errors.generation_failed", { detail: data.data.error?.detail || "Unknown error" }))
            dispatch({ type: 'SET_LOADING', payload: false })
            return
          }
        }

        attempts++
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000)
        } else {
          toast.error(t("errors.network_error"))
          dispatch({ type: 'SET_LOADING', payload: false })
        }
      } catch (error) {
        console.error('Polling error:', error)
        attempts++
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000)
        } else {
          dispatch({ type: 'SET_LOADING', payload: false })
        }
      }
    }

    poll()
  }, [state, t, fetchUserCredits])
  
  const value = {
    state,
    dispatch,
    form,
    handleGenerate,
    handleModelSelect,
    fetchUserCredits,
    loadModels
  }
  
  return (
    <AIGenerationContext.Provider value={value}>
      {children}
    </AIGenerationContext.Provider>
  )
}

// Hook
export function useAIGeneration() {
  const context = useContext(AIGenerationContext)
  if (!context) {
    throw new Error('useAIGeneration must be used within AIGenerationProvider')
  }
  return context
}
```

### 3. 修改现有组件

#### 3.1 修改 input-main.tsx
```typescript
// components/ai-dashboard/input-main.tsx (修改)
"use client";

import { AIGenerationProvider } from "./components/hooks/useAIGeneration";
import { ModelTypeTabs } from "./components/input/ModelTypeTabs";
import { ModelSelector } from "./components/input/ModelSelector";
import { PromptInput } from "./components/input/PromptInput";
import { DynamicOptionsConfig } from "./components/input/DynamicOptionsConfig";
import { CostEstimate } from "./components/input/CostEstimate";
import { GenerateButton } from "./components/input/GenerateButton";

interface AINonFullMainProps {
  modelType?: string;
  onResultChange?: (result: GenerationResult | null) => void;
  onGeneratingChange?: (isGenerating: boolean) => void;
}

export default function AINonFullMain({ 
  modelType, 
  onResultChange, 
  onGeneratingChange 
}: AINonFullMainProps) {
  return (
    <AIGenerationProvider modelType={modelType}>
      <AIFormContent 
        onResultChange={onResultChange}
        onGeneratingChange={onGeneratingChange}
      />
    </AIGenerationProvider>
  );
}

function AIFormContent({ onResultChange, onGeneratingChange }: {
  onResultChange?: (result: GenerationResult | null) => void;
  onGeneratingChange?: (isGenerating: boolean) => void;
}) {
  const { form, generationResult, loading } = useAIGeneration();
  
  // 向上传递状态变化（保持现有接口兼容）
  useEffect(() => {
    onResultChange?.(generationResult);
  }, [generationResult, onResultChange]);
  
  useEffect(() => {
    onGeneratingChange?.(loading);
  }, [loading, onGeneratingChange]);
  
  return (
    <form className="space-y-4">
      <ModelTypeTabs />
      <ModelSelector />
      <PromptInput />
      <DynamicOptionsConfig />
      <CostEstimate />
      <GenerateButton />
    </form>
  );
}
```

#### 3.2 修改 PromptInput.tsx
```typescript
// components/ai-dashboard/components/input/PromptInput.tsx (修改)
"use client";

import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../hooks/use-device-layout";
import { useAIGeneration } from "../hooks/useAIGeneration";

export function PromptInput() {
  const t = useTranslations("ai-dashboard.generator");
  const { isMobile, isSmallMobile } = useDeviceLayout();
  const { form } = useAIGeneration();

  return (
    <div className={`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full ${
      isSmallMobile ? 'px-1.5 py-1' :
      isMobile ? 'px-2 py-1.5' : 'px-3 py-2'
    }`}>
      <Label htmlFor="prompt" className={`font-medium text-foreground ${
        isSmallMobile ? 'text-xs' :
        isMobile ? 'text-xs' : 'text-sm'
      }`}>{t("prompt_input")}</Label>
      <Textarea
        id="prompt"
        placeholder={t("prompt_placeholder")}
        {...form.register("prompt")}
        rows={isSmallMobile ? 2 : isMobile ? 3 : 4}
        className={`bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border resize-none rounded-xl w-full max-w-full ${
          isSmallMobile ? 'mt-0.5 text-xs' :
          isMobile ? 'mt-1 text-sm' : 'mt-2 text-base'
        }`}
      />
      {form.formState.errors.prompt && (
        <p className="text-sm text-destructive mt-1">
          {form.formState.errors.prompt.message}
        </p>
      )}
    </div>
  );
}
```

#### 3.3 修改 output/ResultDisplay.tsx
```typescript
// components/ai-dashboard/components/output/ResultDisplay.tsx (修改)
"use client";

import { useAIGeneration } from "../hooks/useAIGeneration";

export function ResultDisplay() {
  const { 
    form, 
    selectedModel, 
    generationResult, 
    generationHistory,
    loading 
  } = useAIGeneration();
  
  const formData = form.getValues();
  
  return (
    <div className="result-display">
      {/* 显示当前配置信息 */}
      <div className="generation-config">
        <h3>当前配置</h3>
        <p>模型: {selectedModel?.model_name}</p>
        <p>类型: {formData.modelType}</p>
        <p>提示词: {formData.prompt}</p>
        {/* 显示其他配置信息 */}
      </div>
      
      {/* 显示生成结果 */}
      {generationResult && (
        <div className="generation-result">
          {/* 现有的结果显示逻辑 */}
        </div>
      )}
      
      {/* 显示历史记录 */}
      {generationHistory.length > 0 && (
        <div className="generation-history">
          <h3>历史记录</h3>
          {generationHistory.map((item, index) => (
            <div key={index} className="history-item">
              <p>模型: {item.selectedModel?.model_name}</p>
              <p>提示词: {item.formData?.prompt}</p>
              <p>时间: {item.timestamp?.toLocaleString()}</p>
              {/* 显示结果 */}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

## 实施计划

### Phase 1: 基础架构搭建 (1-2天)
1. 创建 Zod schemas
2. 实现 Reducer 和 Context
3. 创建基础 hooks

### Phase 2: 核心组件重构 (2-3天)
1. 重构 PromptInput
2. 重构 ModelSelector
3. 重构 OptionsConfig
4. 重构 GenerateButton

### Phase 3: 高级功能实现 (2-3天)
1. 动态选项配置
2. 成本估算集成
3. 错误处理优化
4. 性能优化

### Phase 4: Output 组件增强 (1-2天)
1. 重构 ResultDisplay
2. 添加配置信息显示
3. 实现历史记录功能
4. 添加分享和下载功能

### Phase 5: 测试和优化 (1-2天)
1. 单元测试
2. 集成测试
3. 性能测试
4. 用户体验优化

## 预期收益

### 开发体验
- **类型安全**: 编译时和运行时类型检查
- **代码提示**: 更好的 IDE 支持
- **错误处理**: 统一的错误处理机制
- **调试友好**: 清晰的状态流转

### 用户体验
- **响应速度**: 减少不必要的重渲染
- **错误反馈**: 实时表单验证
- **状态保持**: 表单状态持久化
- **功能完整**: output 组件获得完整信息

### 维护性
- **代码组织**: 清晰的职责分离
- **扩展性**: 易于添加新功能
- **测试性**: 更容易编写测试
- **文档化**: 自文档化的类型定义



## 总结


这个重构方案将显著提升 AI Dashboard 的开发体验和用户体验，通过引入现代化的表单管理和状态管理模式，解决当前架构的痛点，为后续功能扩展奠定坚实基础。






需要重点考虑失败经验问题总结
1. JSX 语法错误
问题: Expected '>', got 'value' 语法错误
原因: 重构时没考虑到在 TypeScript 文件中 JSX 属性值需要用花括号包裹，直接写成了 value={value} 而不是正确的语法
解决: 最终使用 React.createElement 避免 JSX 语法问题
2. React 未定义错误
问题: React is not defined
原因: 使用了 React.createElement 但忘记导入 React
解决: 添加 import React 导入
3. Context 依赖错误
问题: useAIGeneration must be used within AIGenerationProvider
原因: 重构时没考虑到现有组件（如 ModelTypeTabs、ResultDisplay）在 workspace-layout.tsx 中被直接使用，没有 Provider 包裹

4. 无限循环错误
问题: Maximum update depth exceeded 无限重渲染
原因: 重构时没考虑到 React Hook 的依赖管理：
form.watch() 每次渲染返回新对象导致 useEffect 无限触发
useEffect 依赖数组包含了每次渲染都重新创建的函数
解决:
使用 form.watch(callback) 订阅模式替代直接调用
移除会导致循环的依赖，添加 eslint-disable 注释
5. 翻译键缺失错误
问题: MISSING_MESSAGE: Could not resolve ai-dashboard.models.types.text
原因: 重构时没考虑到翻译文件的实际结构，直接假设了翻译键的路径
解决: 查看实际翻译文件，修正为正确的翻译键路径 ai-dashboard.tabs.text
核心问题
重构时主要没考虑到：


React Hook 规则 - useEffect 依赖管理和无限循环问题
实际项目结构 - 翻译文件的真实组织方式
JSX 语法细节 - TypeScript 环境下的 JSX 语法要求