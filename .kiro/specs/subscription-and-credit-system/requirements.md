# 需求文档

## 介绍

此功能引入了一个混合支付系统，支持基于订阅的计费和一次性积分购买。系统将允许用户订阅月度或年度计划，提供每月重置的循环积分，同时也支持永不过期的一次性积分包。这为不同用户需求提供了灵活性 - 常规用户可以订阅以获得持续访问，偶尔使用的用户可以根据需要购买积分。

## 需求

### 需求 1

**用户故事：** 作为平台用户，我希望能在订阅计划和一次性积分购买之间选择，以便我可以选择最适合我使用模式的付费模式。

#### 验收标准

1. 当用户访问定价页面时，系统应显示订阅计划和一次性积分包
2. 当用户选择订阅计划时，系统应通过 Stripe 启动循环计费设置
3. 当用户选择一次性积分包时，系统应处理单次付款交易
4. 如果用户有活跃订阅，系统仍应允许购买额外的一次性积分包

### 需求 2

**用户故事：** 作为订阅用户，我希望我的订阅积分每月重置，无论我的计费周期如何，以便我每月都能持续访问 AI 服务。

#### 验收标准

1. 当用户订阅月度计划时，系统应立即分配计划的积分并设置月度重置日期
2. 当用户订阅年度计划时，系统应立即分配计划的积分并设置月度重置日期
3. 当月度重置日期到达时，系统应将订阅积分重置为计划的完整数量
4. 当订阅积分重置时，系统应保留任何未使用的一次性积分
5. 如果用户取消订阅，系统应停止积分重置但保留现有积分直到过期

### 需求 3

**用户故事：** 作为购买一次性积分包的用户，我希望我的积分永不过期，以便我可以按自己的节奏使用它们，没有时间压力。

#### 验收标准

1. 当用户购买一次性积分包时，系统应将积分添加到其账户且无过期日期
2. 当系统计算可用积分时，应包括订阅积分和不过期的一次性积分
3. 当消耗积分时，系统应优先使用订阅积分，然后使用一次性积分
4. 当显示积分余额时，系统应显示总积分并指示哪部分来自订阅与一次性购买

### 需求 4

**用户故事：** 作为平台管理员，我希望管理订阅计划和定价，以便我可以根据市场需求和业务要求调整产品。

#### 验收标准

1. 当管理员访问管理仪表板时，系统应显示订阅计划管理界面
2. 当管理员创建新订阅计划时，系统应允许设置月度积分数量、计费频率和价格
3. 当管理员修改现有计划时，系统应仅将更改应用于新订阅，保留现有订阅者条款
4. 当管理员创建一次性积分包时，系统应允许设置积分数量和价格且无过期时间

### 需求 5

**用户故事：** 作为用户，我希望看到清晰的账单历史和积分使用跟踪，以便我可以了解我的支出和消费模式。

#### 验收标准

1. 当用户访问其账户仪表板时，系统应显示按来源分解的当前积分余额（订阅与一次性）
2. 当用户查看账单历史时，系统应显示所有订阅付款和一次性购买的日期和金额
3. 当用户查看积分使用情况时，系统应显示带有时间戳和剩余余额的消费历史
4. 当订阅积分重置时，系统应在用户的积分历史中记录重置事件

### 需求 6

**用户故事：** 作为有活跃订阅的用户，我希望升级、降级或取消我的订阅，以便我可以根据变化的需求调整我的计划。

#### 验收标准

1. 当用户升级订阅时，系统应按比例计算计费差额并立即调整积分分配
2. 当用户降级订阅时，系统应在下一个计费周期应用更改并保留多余积分
3. 当用户取消订阅时，系统应停止未来计费但保持访问直到当前期间结束
4. 当订阅更改发生时，系统应发送确认邮件并更新用户仪表板

### 需求 7

**用户故事：** 作为平台运营者，我希望自动化订阅管理和计费，以便系统可以处理循环付款和积分分配而无需手动干预。

#### 验收标准

1. 当订阅计费日期到达时，系统应通过 creem 自动处理付款
2. 当订阅付款成功时，系统应继续订阅并维持积分重置计划
3. 当订阅付款失败时，系统应根据 creem 的重试逻辑重试并通知用户
4. 如果订阅付款反复失败，系统应暂停订阅并停止积分重置
5. 当订阅状态更改时，系统应更新用户账户状态并发送适当通知