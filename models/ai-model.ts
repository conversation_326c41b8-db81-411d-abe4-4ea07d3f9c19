import { getSupabaseClient } from "./db";

export interface AIModel {
  id?: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: string;
  is_active: boolean;
  description?: string;
  description_i18n?: Record<string, string>;
  model_name_i18n?: Record<string, string>;
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AIModelUsage {
  id?: number;
  user_uuid: string;
  model_id: string;
  task_id: string;
  provider: string;
  external_request_id?: string;
  input_size?: number;
  output_size?: number;
  credits_consumed: number;
  status: string;
  error_reason?: string;
  error_detail?: string;
  request_params?: any;
  response_data?: any;
  started_at?: string;
  completed_at?: string;
  created_at?: string;
}

/**
 * 简单的成本计算函数（基于本地模型配置）
 */
export function calculateModelCost(
  creditsPerUnit: number,
  inputSize?: number,
  outputSize?: number
): number {
  // 对于文本模型，可以基于token数量进行更精确的计算
  // 目前简化为固定的单位成本
  return creditsPerUnit;
}

/**
 * 创建模型使用记录
 */
export async function createAIModelUsage(usage: AIModelUsage): Promise<AIModelUsage> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .insert(usage)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}



/**
 * 通过任务ID更新模型使用记录
 */
export async function updateAIModelUsageByTaskId(
  taskId: string,
  updates: Partial<AIModelUsage>
): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .update(updates)
    .eq("task_id", taskId)
    .select()
    .single();

  if (error) {
    return null;
  }

  return data;
}



/**
 * 根据外部请求ID获取使用记录
 */
export async function getAIModelUsageByExternalRequestId(externalRequestId: string): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("external_request_id", externalRequestId)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 根据任务ID获取使用记录
 */
export async function getAIModelUsageByTaskId(taskId: string): Promise<AIModelUsage | null> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("task_id", taskId)
    .single();

  if (error) {
    return null;
  }

  return data;
}

/**
 * 获取用户的模型使用记录
 */
export async function getUserAIModelUsage(
  userUuid: string,
  page: number = 1,
  limit: number = 50
): Promise<AIModelUsage[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("ai_model_usage")
    .select("*")
    .eq("user_uuid", userUuid)
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

/**
 * 获取用户积分使用统计
 */
export async function getUserCreditsUsageStats(userUuid: string) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("user_credits_usage_stats")
    .select("*")
    .eq("user_uuid", userUuid)
    .order("total_credits_consumed", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}

/**
 * 获取模型使用统计
 */
export async function getModelUsageStats() {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("model_usage_stats")
    .select("*")
    .order("total_usage_count", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}


