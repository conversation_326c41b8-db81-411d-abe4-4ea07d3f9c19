// config/product-config.ts
export interface ProductConfig {
  base_id: string; // 基础产品ID，用于内部识别
  name: string;
  tier: 'lite' | 'pro' | 'premium' | 'small';
  interval: 'month' | 'year' | 'one-time';
  providers: {
    stripe?: {
      live: string;  // 生产环境产品ID
      test: string;  // 测试环境产品ID
    };
    creem?: {
      live: string;  // 生产环境产品ID
      test: string;  // 测试环境产品ID
    };
  };
}

// 产品配置映射表
export const PRODUCT_CONFIGS: ProductConfig[] = [
  // Lite 套餐
  {
    base_id: 'lite_monthly',
    name: 'Lite Monthly',
    tier: 'lite',
    interval: 'month',
    providers: {
      stripe: {
        live: 'lite_monthly',
        test: 'lite_monthly_test'
      },
      creem: {
        live: 'creem_lite_monthly',
        test: 'prod_78bRDLKVhrBlYr0pvhAv0e'
      }
    }
  },
  {
    base_id: 'lite_yearly',
    name: 'Lite Yearly',
    tier: 'lite',
    interval: 'year',
    providers: {
      stripe: {
        live: 'lite_yearly',
        test: 'lite_yearly_test'
      },
      creem: {
        live: 'creem_lite_yearly',
        test: 'creem_lite_yearly_test'
      }
    }
  },
  // Pro 套餐
  {
    base_id: 'pro_monthly',
    name: 'Pro Monthly',
    tier: 'pro',
    interval: 'month',
    providers: {
      stripe: {
        live: 'pro_monthly',
        test: 'pro_monthly_test'
      },
      creem: {
        live: 'creem_pro_monthly',
        test: 'prod_5XBjBh7D64exf5j5W4pghQ'
      }
    }
  },
  {
    base_id: 'pro_yearly',
    name: 'Pro Yearly',
    tier: 'pro',
    interval: 'year',
    providers: {
      stripe: {
        live: 'pro_yearly',
        test: 'pro_yearly_test'
      },
      creem: {
        live: 'creem_pro_yearly',
        test: 'creem_pro_yearly_test'
      }
    }
  },
  // Premium 套餐
  {
    base_id: 'premium_monthly',
    name: 'Premium Monthly',
    tier: 'premium',
    interval: 'month',
    providers: {
      stripe: {
        live: 'premium_monthly',
        test: 'premium_monthly_test'
      },
      creem: {
        live: 'creem_premium_monthly',
        test: 'creem_premium_monthly_test'
      }
    }
  },
  {
    base_id: 'premium_yearly',
    name: 'Premium Yearly',
    tier: 'premium',
    interval: 'year',
    providers: {
      stripe: {
        live: 'premium_yearly',
        test: 'premium_yearly_test'
      },
      creem: {
        live: 'creem_premium_yearly',
        test: 'creem_premium_yearly_test'
      }
    }
  },
  // 一次性产品
  {
    base_id: 'small_package',
    name: 'Small Package',
    tier: 'small',
    interval: 'one-time',
    providers: {
      creem: {
        live: 'prod_3iR9qvXkmMCgYB2cS0rkc3',
        test: 'prod_3iR9qvXkmMCgYB2cS0rkc3'
      }
    }
  }
];

// 检查是否为测试模式
function isTestMode(): boolean {
  return process.env.PAYMENT_TEST_MODE === 'true';
}

// 根据基础产品ID和支付平台获取对应的产品ID
export function getProviderProductId(baseProductId: string, provider: string): string | undefined {
  const config = PRODUCT_CONFIGS.find(c => c.base_id === baseProductId);
  if (!config) {
    throw new Error(`Product config not found for base_id: ${baseProductId}`);
  }

  const providerConfig = config.providers[provider as keyof typeof config.providers];
  if (!providerConfig) {
    throw new Error(`Provider ${provider} not configured for product ${baseProductId}`);
  }

  // 根据测试模式返回对应的产品ID
  const mode = isTestMode() ? 'test' : 'live';
  return providerConfig[mode];
}

// 根据支付平台产品ID获取基础配置
export function getProductConfigByProviderId(providerId: string, provider: string): ProductConfig | undefined {
  return PRODUCT_CONFIGS.find(config => {
    const providerConfig = config.providers[provider as keyof typeof config.providers];
    if (!providerConfig) return false;

    // 检查live和test两种模式的产品ID
    return providerConfig.live === providerId || providerConfig.test === providerId;
  });
}

// 根据支付平台产品ID获取基础产品ID
export function getBaseProductId(providerId: string, provider: string): string | undefined {
  const config = getProductConfigByProviderId(providerId, provider);
  return config?.base_id;
}

// 获取所有支持的产品
export function getAllProducts(): ProductConfig[] {
  return PRODUCT_CONFIGS;
}

// 根据套餐和周期获取产品配置
export function getProductByTierAndInterval(tier: string, interval: string): ProductConfig | undefined {
  return PRODUCT_CONFIGS.find(config =>
    config.tier === tier && config.interval === interval
  );
}

// 获取当前支付环境信息
export function getPaymentEnvironment() {
  return {
    isTestMode: isTestMode(),
    mode: isTestMode() ? 'test' : 'live',
    enabledProviders: process.env.PAYMENT_PROVIDERS_ENABLED?.split(',') || ['stripe'],
    defaultProvider: process.env.PAYMENT_DEFAULT_PROVIDER || 'stripe'
  };
}
