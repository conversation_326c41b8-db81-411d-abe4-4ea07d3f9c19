# 多支付平台重构指导文档

## 📋 项目概述

本文档详细说明如何将现有的Stripe单一支付系统重构为支持多支付平台（Stripe + Creem）的架构。

## 🎯 重构目标

1. **保持向后兼容性** - 现有Stripe功能不受影响
2. **支持多平台扩展** - 轻松添加新的支付平台
3. **统一API接口** - 前端调用方式保持一致
4. **数据结构优化** - 支持不同平台的特有数据

## 🔍 当前系统分析

### 现有Stripe组件
- **环境变量**: `STRIPE_PUBLIC_KEY`, `STRIPE_PRIVATE_KEY`, `STRIPE_WEBHOOK_SECRET`
- **API路由**: `/api/checkout`, `/api/stripe-notify`
- **数据库字段**: `orders.stripe_session_id`
- **前端组件**: `PricingContent.tsx`
- **服务函数**: `services/order.ts`

### Creem平台特性
- **API认证**: 使用`x-api-key`头部认证
- **创建支付**: `POST /v1/checkout`
- **订阅管理**: `POST /v1/subscriptions/{id}/upgrade`
- **座位计费**: 支持基于用户数量的计费
- **TypeScript SDK**: 提供官方SDK支持

## 🏗️ 重构架构设计

### 1. 数据库结构调整

#### 新增字段
```sql
-- 添加支付平台标识字段
ALTER TABLE orders ADD COLUMN payment_provider VARCHAR(50) NOT NULL DEFAULT 'stripe';

-- 重命名会话ID字段为通用字段
ALTER TABLE orders RENAME COLUMN stripe_session_id TO payment_session_id;

-- 添加平台特定数据存储
ALTER TABLE orders ADD COLUMN provider_specific_data JSONB;

-- 为现有数据设置默认值
UPDATE orders SET payment_provider = 'stripe' WHERE payment_provider IS NULL;

-- 创建索引优化查询
CREATE INDEX idx_orders_payment_provider ON orders(payment_provider);
CREATE INDEX idx_orders_payment_session_id ON orders(payment_session_id);
```

#### 数据迁移策略
1. **阶段1**: 添加新字段，保持旧字段
2. **阶段2**: 迁移数据到新字段
3. **阶段3**: 更新代码使用新字段
4. **阶段4**: 删除旧字段（可选）

### 2. 环境变量配置

#### 新增配置项
```env
# 现有Stripe配置保持不变
STRIPE_PUBLIC_KEY=""
STRIPE_PRIVATE_KEY=""
STRIPE_WEBHOOK_SECRET=""

# 新增Creem配置
CREEM_API_KEY=""
CREEM_WEBHOOK_SECRET=""

# 通用支付配置
PAYMENT_DEFAULT_PROVIDER="stripe"
PAYMENT_PROVIDERS_ENABLED="stripe,creem"

# 支付回调URL（支持多平台）
NEXT_PUBLIC_PAY_SUCCESS_URL="http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL="http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL="http://localhost:3000/#pricing"
```

### 3. 支付抽象层设计

#### 核心接口定义
```typescript
// services/payment/types.ts
export interface PaymentProvider {
  createCheckoutSession(params: CheckoutParams): Promise<PaymentSession>;
  handleWebhook(data: any, signature: string): Promise<WebhookResult>;
  retrieveSession(sessionId: string): Promise<SessionData>;
  validateConfig(): boolean;
}

export interface CheckoutParams {
  amount: number;
  currency: string;
  productName: string;
  productId: string;
  customerEmail?: string;
  successUrl: string;
  cancelUrl: string;
  metadata: Record<string, string>;
  isSubscription?: boolean;
  interval?: string;
}

export interface PaymentSession {
  id: string;
  url: string;
  provider: string;
  metadata?: Record<string, any>;
}

export interface WebhookResult {
  success: boolean;
  orderNo?: string;
  sessionId?: string;
  error?: string;
}
```

#### 支付工厂类
```typescript
// services/payment/factory.ts
export class PaymentFactory {
  private static providers: Map<string, PaymentProvider> = new Map();

  static createProvider(provider: string): PaymentProvider {
    if (this.providers.has(provider)) {
      return this.providers.get(provider)!;
    }

    let providerInstance: PaymentProvider;
    
    switch (provider) {
      case 'stripe':
        providerInstance = new StripeProvider(process.env.STRIPE_PRIVATE_KEY!);
        break;
      case 'creem':
        providerInstance = new CreemProvider(process.env.CREEM_API_KEY!);
        break;
      default:
        throw new Error(`Unsupported payment provider: ${provider}`);
    }

    // 验证配置
    if (!providerInstance.validateConfig()) {
      throw new Error(`Invalid configuration for provider: ${provider}`);
    }

    this.providers.set(provider, providerInstance);
    return providerInstance;
  }

  static getEnabledProviders(): string[] {
    return process.env.PAYMENT_PROVIDERS_ENABLED?.split(',') || ['stripe'];
  }

  static getDefaultProvider(): string {
    return process.env.PAYMENT_DEFAULT_PROVIDER || 'stripe';
  }
}
```

### 4. Stripe Provider重构

#### 重命名现有函数
```typescript
// services/payment/stripe.ts
export class StripeProvider implements PaymentProvider {
  private stripe: Stripe;
  
  constructor(privateKey: string) {
    this.stripe = new Stripe(privateKey);
  }

  validateConfig(): boolean {
    return !!(process.env.STRIPE_PRIVATE_KEY && process.env.STRIPE_PUBLIC_KEY);
  }

  async createCheckoutSession(params: CheckoutParams): Promise<PaymentSession> {
    const sessionOptions: Stripe.Checkout.SessionCreateParams = {
      payment_method_types: ['card'],
      line_items: [{
        price_data: {
          currency: params.currency,
          product_data: { name: params.productName },
          unit_amount: params.amount,
          recurring: params.isSubscription ? {
            interval: params.interval as Stripe.Price.Recurring.Interval
          } : undefined
        },
        quantity: 1
      }],
      mode: params.isSubscription ? 'subscription' : 'payment',
      success_url: params.successUrl,
      cancel_url: params.cancelUrl,
      customer_email: params.customerEmail,
      metadata: params.metadata
    };

    // 支持中国支付方式
    if (params.currency === 'cny') {
      sessionOptions.payment_method_types = ['wechat_pay', 'alipay', 'card'];
      sessionOptions.payment_method_options = {
        wechat_pay: { client: 'web' },
        alipay: {}
      };
    }

    const session = await this.stripe.checkout.sessions.create(sessionOptions);
    
    return {
      id: session.id,
      url: session.url!,
      provider: 'stripe',
      metadata: { stripeSessionId: session.id }
    };
  }

  async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
    try {
      const event = await this.stripe.webhooks.constructEventAsync(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      );

      if (event.type === 'checkout.session.completed') {
        const session = event.data.object as Stripe.Checkout.Session;
        return {
          success: true,
          orderNo: session.metadata?.order_no,
          sessionId: session.id
        };
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async retrieveSession(sessionId: string): Promise<SessionData> {
    const session = await this.stripe.checkout.sessions.retrieve(sessionId);
    return {
      id: session.id,
      paymentStatus: session.payment_status,
      customerEmail: session.customer_details?.email,
      metadata: session.metadata || {}
    };
  }
}
```

### 5. Creem Provider实现

```typescript
// services/payment/creem.ts
export class CreemProvider implements PaymentProvider {
  private apiKey: string;
  private baseUrl = 'https://api.creem.io/v1';
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  validateConfig(): boolean {
    return !!this.apiKey;
  }

  async createCheckoutSession(params: CheckoutParams): Promise<PaymentSession> {
    const requestBody = {
      line_items: [{
        price_data: {
          currency: params.currency,
          unit_amount: params.amount,
          product_data: {
            name: params.productName
          }
        },
        quantity: 1
      }],
      mode: params.isSubscription ? 'subscription' : 'payment',
      success_url: params.successUrl,
      cancel_url: params.cancelUrl,
      customer_email: params.customerEmail,
      metadata: params.metadata
    };

    const response = await fetch(`${this.baseUrl}/checkout`, {
      method: 'POST',
      headers: {
        'x-api-key': this.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Creem API error: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      id: data.id,
      url: data.url,
      provider: 'creem',
      metadata: { creemSessionId: data.id }
    };
  }

  async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
    // Creem webhook验证逻辑
    try {
      const event = JSON.parse(body);
      
      // 验证签名（根据Creem文档实现）
      if (!this.verifyWebhookSignature(body, signature)) {
        return { success: false, error: 'Invalid signature' };
      }

      if (event.type === 'checkout.session.completed') {
        return {
          success: true,
          orderNo: event.data.metadata?.order_no,
          sessionId: event.data.id
        };
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async retrieveSession(sessionId: string): Promise<SessionData> {
    const response = await fetch(`${this.baseUrl}/checkout/${sessionId}`, {
      headers: {
        'x-api-key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to retrieve Creem session: ${response.statusText}`);
    }

    const session = await response.json();
    return {
      id: session.id,
      paymentStatus: session.status,
      customerEmail: session.customer_email,
      metadata: session.metadata || {}
    };
  }

  private verifyWebhookSignature(body: string, signature: string): boolean {
    // 实现Creem的webhook签名验证
    // 具体实现需要参考Creem文档
    return true; // 临时返回true，需要实际实现
  }
}
```

## 📝 实施步骤

### 阶段1：基础架构搭建（1-2天）
1. **数据库结构调整**
   - 执行SQL迁移脚本
   - 验证数据完整性
   
2. **创建支付抽象层**
   - 定义接口和类型
   - 实现工厂模式
   
3. **重构Stripe代码**
   - 将现有代码封装到StripeProvider
   - 更新函数命名

### 阶段2：Creem集成（2-3天）
1. **实现CreemProvider**
   - API调用逻辑
   - Webhook处理
   
2. **更新API路由**
   - 支持多平台选择
   - 统一错误处理
   
3. **环境变量配置**
   - 添加Creem配置
   - 更新部署脚本

### 阶段3：前端更新（1-2天）
1. **支付平台选择器**
   - UI组件开发
   - 状态管理
   
2. **支付流程适配**
   - 不同平台的跳转逻辑
   - 错误处理优化

### 阶段4：测试与优化（1-2天）
1. **沙盒环境测试**
   - 两个平台的支付流程
   - Webhook接收验证
   
2. **性能优化**
   - 缓存策略
   - 错误监控

## ⚠️ 注意事项

### 数据安全
- 支付数据迁移前务必备份
- 敏感信息加密存储
- API密钥安全管理

### 向后兼容
- 保持现有API接口不变
- 渐进式迁移策略
- 回滚方案准备

### 错误处理
- 统一错误码定义
- 详细日志记录
- 用户友好的错误提示

### 监控告警
- 支付成功率监控
- API响应时间监控
- 异常情况告警

## 🔧 配置管理

### 开发环境
```env
PAYMENT_DEFAULT_PROVIDER="stripe"
PAYMENT_PROVIDERS_ENABLED="stripe"
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_PRIVATE_KEY="sk_test_..."
```

### 生产环境
```env
PAYMENT_DEFAULT_PROVIDER="stripe"
PAYMENT_PROVIDERS_ENABLED="stripe,creem"
STRIPE_PUBLIC_KEY="pk_live_..."
STRIPE_PRIVATE_KEY="sk_live_..."
CREEM_API_KEY="creem_live_..."
```

## 📊 成功指标

- **功能完整性**: 所有现有支付功能正常
- **性能指标**: API响应时间 < 2秒
- **成功率**: 支付成功率 > 99%
- **用户体验**: 支付流程无缝切换

## 🔄 API路由重构详解

### 1. 更新 `/api/checkout` 路由

```typescript
// app/api/checkout/route.ts
import { PaymentFactory } from '@/services/payment/factory';
import { respData, respErr } from '@/lib/resp';
import { getUserEmail, getUserUuid } from '@/services/user';
import { insertOrder, updateOrderPaymentSession } from '@/models/order';

export async function POST(req: Request) {
  try {
    const {
      provider = PaymentFactory.getDefaultProvider(),
      credits,
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
    } = await req.json();

    // 验证支付平台
    const enabledProviders = PaymentFactory.getEnabledProviders();
    if (!enabledProviders.includes(provider)) {
      return respErr(`Unsupported payment provider: ${provider}`);
    }

    // 现有的参数验证逻辑保持不变
    if (!amount || !interval || !currency || !product_id) {
      return respErr("invalid params");
    }

    // 用户验证
    const user_uuid = await getUserUuid();
    const user_email = await getUserEmail();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // 创建订单（现有逻辑）
    const order_no = getSnowId();
    const order: Order = {
      order_no,
      created_at: getIsoTimestr(),
      user_uuid,
      user_email,
      amount,
      interval,
      expired_at: getOneYearLaterTimestr(),
      status: "created",
      credits,
      currency,
      product_id,
      product_name,
      valid_months,
      payment_provider: provider, // 新增字段
    };

    await insertOrder(order);

    // 使用支付工厂创建会话
    const paymentProvider = PaymentFactory.createProvider(provider);
    const session = await paymentProvider.createCheckoutSession({
      amount,
      currency,
      productName: product_name,
      productId: product_id,
      customerEmail: user_email,
      successUrl: `${process.env.NEXT_PUBLIC_WEB_URL}/pay-success/${provider}/{CHECKOUT_SESSION_ID}`,
      cancelUrl: cancel_url || process.env.NEXT_PUBLIC_PAY_CANCEL_URL,
      metadata: {
        project: process.env.NEXT_PUBLIC_PROJECT_NAME || "",
        product_name,
        order_no: order_no.toString(),
        user_email,
        credits: credits.toString(),
        user_uuid,
      },
      isSubscription: interval !== 'one-time',
      interval,
    });

    // 更新订单会话信息
    await updateOrderPaymentSession(
      order_no,
      session.id,
      provider,
      JSON.stringify(session)
    );

    return respData({
      session_id: session.id,
      payment_url: session.url,
      provider,
      order_no,
    });
  } catch (e: any) {
    console.log("checkout failed: ", e);
    return respErr("checkout failed: " + e.message);
  }
}
```

### 2. 创建通用Webhook处理器

```typescript
// app/api/payment-notify/[provider]/route.ts
import { PaymentFactory } from '@/services/payment/factory';
import { handlePaymentCallback } from '@/services/order';
import { respOk, respErr } from '@/lib/resp';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ provider: string }> }
) {
  try {
    const { provider } = await params;

    // 验证支付平台
    const enabledProviders = PaymentFactory.getEnabledProviders();
    if (!enabledProviders.includes(provider)) {
      return respErr(`Unsupported payment provider: ${provider}`);
    }

    const body = await req.text();
    const signature = req.headers.get(`${provider}-signature`) ||
                     req.headers.get('stripe-signature') ||
                     req.headers.get('x-creem-signature') || '';

    if (!signature || !body) {
      return respErr("invalid notify data");
    }

    // 使用对应的支付提供商处理webhook
    const paymentProvider = PaymentFactory.createProvider(provider);
    const result = await paymentProvider.handleWebhook(body, signature);

    if (result.success && result.orderNo && result.sessionId) {
      // 调用通用的订单处理逻辑
      await handlePaymentCallback(provider, result.sessionId, result.orderNo);
    }

    return respOk();
  } catch (e: any) {
    console.log(`${provider} webhook failed:`, e);
    return respErr(`webhook failed: ${e.message}`);
  }
}
```

### 3. 保持Stripe专用Webhook（向后兼容）

```typescript
// app/api/stripe-notify/route.ts (保持现有逻辑)
import { StripeProvider } from '@/services/payment/stripe';
import { handlePaymentCallback } from '@/services/order';
import { respOk } from '@/lib/resp';

export async function POST(req: Request) {
  try {
    const stripeProvider = new StripeProvider(process.env.STRIPE_PRIVATE_KEY!);
    const body = await req.text();
    const signature = req.headers.get("stripe-signature") as string;

    const result = await stripeProvider.handleWebhook(body, signature);

    if (result.success && result.orderNo && result.sessionId) {
      await handlePaymentCallback('stripe', result.sessionId, result.orderNo);
    }

    return respOk();
  } catch (e: any) {
    console.log("stripe webhook failed:", e);
    return respErr(`webhook failed: ${e.message}`);
  }
}
```

## 🎨 前端组件重构

### 1. 支付平台选择器组件

```typescript
// components/payment/PaymentProviderSelector.tsx
'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface PaymentProvider {
  id: string;
  name: string;
  description: string;
  logo?: string;
  enabled: boolean;
}

interface PaymentProviderSelectorProps {
  providers: PaymentProvider[];
  selectedProvider: string;
  onProviderChange: (provider: string) => void;
}

export default function PaymentProviderSelector({
  providers,
  selectedProvider,
  onProviderChange,
}: PaymentProviderSelectorProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">选择支付方式</h3>
      <RadioGroup value={selectedProvider} onValueChange={onProviderChange}>
        {providers.filter(p => p.enabled).map((provider) => (
          <div key={provider.id} className="flex items-center space-x-2">
            <RadioGroupItem value={provider.id} id={provider.id} />
            <Label htmlFor={provider.id} className="flex-1">
              <Card className="cursor-pointer hover:bg-gray-50">
                <CardContent className="flex items-center p-4">
                  {provider.logo && (
                    <img
                      src={provider.logo}
                      alt={provider.name}
                      className="w-8 h-8 mr-3"
                    />
                  )}
                  <div>
                    <div className="font-medium">{provider.name}</div>
                    <div className="text-sm text-gray-500">{provider.description}</div>
                  </div>
                </CardContent>
              </Card>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}
```

### 2. 更新PricingContent组件

```typescript
// components/shared/PricingContent.tsx (部分更新)
import PaymentProviderSelector from '@/components/payment/PaymentProviderSelector';

export default function PricingContent({ pricing, isModal }: PricingContentProps) {
  const [selectedProvider, setSelectedProvider] = useState('stripe');
  const [availableProviders, setAvailableProviders] = useState<PaymentProvider[]>([]);

  useEffect(() => {
    // 获取可用的支付平台
    fetch('/api/payment/providers')
      .then(res => res.json())
      .then(data => setAvailableProviders(data.providers));
  }, []);

  const handleCheckout = async (productId: string) => {
    setIsLoading(true);
    setProductId(productId);

    try {
      const item = pricing.items.find(item => item.product_id === productId);
      if (!item) {
        toast.error("Product not found");
        return;
      }

      const response = await fetch("/api/checkout", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          provider: selectedProvider, // 新增支付平台选择
          credits: item.credits,
          currency: item.currency,
          amount: item.amount,
          interval: item.interval,
          product_id: item.product_id,
          product_name: item.product_name,
          valid_months: item.valid_months,
        }),
      });

      if (response.status === 401) {
        // 现有的登录逻辑
        return;
      }

      const { code, message, data } = await response.json();
      if (code !== 0) {
        toast.error(message);
        return;
      }

      const { session_id, payment_url, provider } = data;

      // 根据支付平台处理跳转
      if (provider === 'stripe') {
        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!);
        if (!stripe) {
          toast.error("Stripe initialization failed");
          return;
        }

        const result = await stripe.redirectToCheckout({ sessionId: session_id });
        if (result.error) {
          toast.error(result.error.message);
        }
      } else {
        // 其他支付平台直接跳转
        window.location.href = payment_url;
      }
    } catch (e) {
      console.log("checkout failed: ", e);
      toast.error("checkout failed");
    } finally {
      setIsLoading(false);
      setProductId(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* 支付平台选择器 */}
      <PaymentProviderSelector
        providers={availableProviders}
        selectedProvider={selectedProvider}
        onProviderChange={setSelectedProvider}
      />

      {/* 现有的定价表格 */}
      {/* ... 现有代码保持不变 ... */}
    </div>
  );
}
```

## 📊 数据库模型更新

### 1. 更新Order类型定义

```typescript
// types/order.d.ts
export interface Order {
  order_no: string;
  created_at: string;
  user_uuid: string;
  user_email: string;
  amount: number;
  interval: string;
  expired_at: string;
  status: string;
  payment_session_id?: string; // 重命名自stripe_session_id
  payment_provider: string; // 新增字段
  provider_specific_data?: Record<string, any>; // 新增字段
  credits: number;
  currency: string;
  sub_id?: string;
  sub_interval_count?: number;
  sub_cycle_anchor?: number;
  sub_period_end?: number;
  sub_period_start?: number;
  sub_times?: number;
  product_id?: string;
  product_name?: string;
  valid_months?: number;
  order_detail?: string;
  paid_at?: string;
  paid_email?: string;
  paid_detail?: string;
}
```

### 2. 更新数据库操作函数

```typescript
// models/order.ts (新增函数)
export async function updateOrderPaymentSession(
  order_no: string,
  payment_session_id: string,
  payment_provider: string,
  order_detail: string
) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .update({
      payment_session_id,
      payment_provider,
      order_detail
    })
    .eq("order_no", order_no);

  if (error) {
    throw error;
  }

  return data;
}

export async function findOrderByPaymentSession(
  payment_session_id: string,
  payment_provider: string
): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("payment_session_id", payment_session_id)
    .eq("payment_provider", payment_provider)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}
```

## 🔧 服务层重构

### 1. 通用订单处理函数

```typescript
// services/order.ts (新增函数)
export async function handlePaymentCallback(
  provider: string,
  sessionId: string,
  orderNo?: string
) {
  try {
    // 获取支付会话详情
    const paymentProvider = PaymentFactory.createProvider(provider);
    const sessionData = await paymentProvider.retrieveSession(sessionId);

    // 查找订单
    let order: Order | undefined;
    if (orderNo) {
      order = await findOrderByOrderNo(orderNo);
    } else {
      order = await findOrderByPaymentSession(sessionId, provider);
    }

    if (!order || order.status !== "created") {
      throw new Error("Invalid order or order already processed");
    }

    // 验证支付状态
    if (sessionData.paymentStatus !== 'paid' && sessionData.paymentStatus !== 'complete') {
      throw new Error("Payment not completed");
    }

    // 更新订单状态
    const paid_at = getIsoTimestr();
    const paid_email = sessionData.customerEmail || order.user_email;
    const paid_detail = JSON.stringify(sessionData);

    await updateOrderStatus(order.order_no, "paid", paid_at, paid_email, paid_detail);

    // 发放积分和处理分销
    if (order.credits > 0) {
      await updateCreditForOrder(order);
    }
    await updateAffiliateForOrder(order);

    console.log(`Order ${order.order_no} processed successfully via ${provider}`);
  } catch (error) {
    console.error(`Failed to process ${provider} payment callback:`, error);
    throw error;
  }
}

// 重命名现有函数以明确其Stripe特性
export async function handleStripeOrderSession(session: Stripe.Checkout.Session) {
  // 现有的handleOrderSession逻辑，重命名为handleStripeOrderSession
  // 或者调用新的通用函数
  await handlePaymentCallback('stripe', session.id, session.metadata?.order_no);
}
```

### 2. 支付平台配置API

```typescript
// app/api/payment/providers/route.ts
import { PaymentFactory } from '@/services/payment/factory';
import { respData } from '@/lib/resp';

export async function GET() {
  const enabledProviders = PaymentFactory.getEnabledProviders();
  const defaultProvider = PaymentFactory.getDefaultProvider();

  const providers = [
    {
      id: 'stripe',
      name: 'Stripe',
      description: '支持信用卡、微信支付、支付宝',
      logo: '/images/stripe-logo.png',
      enabled: enabledProviders.includes('stripe')
    },
    {
      id: 'creem',
      name: 'Creem',
      description: '新一代支付解决方案',
      logo: '/images/creem-logo.png',
      enabled: enabledProviders.includes('creem')
    }
  ];

  return respData({
    providers,
    defaultProvider,
    enabledProviders
  });
}
```

## 🧪 测试策略

### 1. 单元测试

```typescript
// tests/payment/stripe.test.ts
import { StripeProvider } from '@/services/payment/stripe';

describe('StripeProvider', () => {
  let stripeProvider: StripeProvider;

  beforeEach(() => {
    stripeProvider = new StripeProvider(process.env.STRIPE_TEST_KEY!);
  });

  test('should create checkout session', async () => {
    const params = {
      amount: 2000,
      currency: 'usd',
      productName: 'Test Product',
      productId: 'prod_test',
      successUrl: 'https://example.com/success',
      cancelUrl: 'https://example.com/cancel',
      metadata: { test: 'true' }
    };

    const session = await stripeProvider.createCheckoutSession(params);

    expect(session.id).toBeDefined();
    expect(session.url).toBeDefined();
    expect(session.provider).toBe('stripe');
  });

  test('should validate config', () => {
    expect(stripeProvider.validateConfig()).toBe(true);
  });
});
```

```typescript
// tests/payment/creem.test.ts
import { CreemProvider } from '@/services/payment/creem';

describe('CreemProvider', () => {
  let creemProvider: CreemProvider;

  beforeEach(() => {
    creemProvider = new CreemProvider(process.env.CREEM_TEST_KEY!);
  });

  test('should create checkout session', async () => {
    const params = {
      amount: 2000,
      currency: 'usd',
      productName: 'Test Product',
      productId: 'prod_test',
      successUrl: 'https://example.com/success',
      cancelUrl: 'https://example.com/cancel',
      metadata: { test: 'true' }
    };

    const session = await creemProvider.createCheckoutSession(params);

    expect(session.id).toBeDefined();
    expect(session.url).toBeDefined();
    expect(session.provider).toBe('creem');
  });
});
```

### 2. 集成测试

```typescript
// tests/api/checkout.test.ts
import { POST } from '@/app/api/checkout/route';

describe('/api/checkout', () => {
  test('should create stripe checkout session', async () => {
    const request = new Request('http://localhost/api/checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        provider: 'stripe',
        amount: 2000,
        currency: 'usd',
        interval: 'one-time',
        product_id: 'prod_test',
        product_name: 'Test Product'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(data.code).toBe(0);
    expect(data.data.provider).toBe('stripe');
    expect(data.data.session_id).toBeDefined();
  });

  test('should create creem checkout session', async () => {
    const request = new Request('http://localhost/api/checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        provider: 'creem',
        amount: 2000,
        currency: 'usd',
        interval: 'one-time',
        product_id: 'prod_test',
        product_name: 'Test Product'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(data.code).toBe(0);
    expect(data.data.provider).toBe('creem');
    expect(data.data.session_id).toBeDefined();
  });
});
```

### 3. E2E测试

```typescript
// tests/e2e/payment.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Payment Flow', () => {
  test('should complete stripe payment', async ({ page }) => {
    await page.goto('/pricing');

    // 选择Stripe支付
    await page.click('[data-testid="payment-provider-stripe"]');

    // 点击购买按钮
    await page.click('[data-testid="buy-button-basic"]');

    // 验证跳转到Stripe
    await expect(page).toHaveURL(/checkout\.stripe\.com/);

    // 填写测试卡信息
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/34');
    await page.fill('[data-testid="card-cvc"]', '123');

    // 完成支付
    await page.click('[data-testid="submit-button"]');

    // 验证支付成功
    await expect(page).toHaveURL(/\/my-orders/);
  });

  test('should complete creem payment', async ({ page }) => {
    await page.goto('/pricing');

    // 选择Creem支付
    await page.click('[data-testid="payment-provider-creem"]');

    // 点击购买按钮
    await page.click('[data-testid="buy-button-basic"]');

    // 验证跳转到Creem
    await expect(page).toHaveURL(/checkout\.creem\.io/);
  });
});
```

## 🚀 部署指南

### 1. 环境变量配置

#### 开发环境 (.env.local)
```env
# 数据库
DATABASE_URL="postgresql://..."
SUPABASE_URL="https://..."
SUPABASE_ANON_KEY="..."

# Stripe测试环境
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_PRIVATE_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Creem测试环境
CREEM_API_KEY="creem_test_..."
CREEM_WEBHOOK_SECRET="creem_webhook_test_..."

# 支付配置
PAYMENT_DEFAULT_PROVIDER="stripe"
PAYMENT_PROVIDERS_ENABLED="stripe,creem"

# 回调URL
NEXT_PUBLIC_PAY_SUCCESS_URL="http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL="http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL="http://localhost:3000/#pricing"
```

#### 生产环境
```env
# Stripe生产环境
STRIPE_PUBLIC_KEY="pk_live_..."
STRIPE_PRIVATE_KEY="sk_live_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Creem生产环境
CREEM_API_KEY="creem_live_..."
CREEM_WEBHOOK_SECRET="creem_webhook_live_..."

# 支付配置
PAYMENT_DEFAULT_PROVIDER="stripe"
PAYMENT_PROVIDERS_ENABLED="stripe,creem"

# 生产回调URL
NEXT_PUBLIC_PAY_SUCCESS_URL="https://yourdomain.com/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL="https://yourdomain.com/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL="https://yourdomain.com/#pricing"
```

### 2. 数据库迁移脚本

```sql
-- migration_001_add_payment_provider.sql
BEGIN;

-- 添加新字段
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_provider VARCHAR(50);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS provider_specific_data JSONB;

-- 为现有数据设置默认值
UPDATE orders SET payment_provider = 'stripe' WHERE payment_provider IS NULL;

-- 设置默认值约束
ALTER TABLE orders ALTER COLUMN payment_provider SET DEFAULT 'stripe';
ALTER TABLE orders ALTER COLUMN payment_provider SET NOT NULL;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_orders_payment_provider ON orders(payment_provider);

COMMIT;
```

```sql
-- migration_002_rename_session_id.sql
BEGIN;

-- 重命名字段
ALTER TABLE orders RENAME COLUMN stripe_session_id TO payment_session_id;

-- 更新索引
DROP INDEX IF EXISTS idx_orders_stripe_session_id;
CREATE INDEX IF NOT EXISTS idx_orders_payment_session_id ON orders(payment_session_id);

COMMIT;
```

### 3. 部署检查清单

#### 部署前检查
- [ ] 数据库迁移脚本已准备
- [ ] 环境变量已配置
- [ ] 测试用例全部通过
- [ ] 代码审查完成
- [ ] 性能测试通过

#### 部署步骤
1. **备份数据库**
   ```bash
   pg_dump -h localhost -U username -d database_name > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **执行数据库迁移**
   ```bash
   psql -h localhost -U username -d database_name -f migration_001_add_payment_provider.sql
   psql -h localhost -U username -d database_name -f migration_002_rename_session_id.sql
   ```

3. **部署代码**
   ```bash
   git checkout main
   git pull origin main
   npm run build
   npm run deploy
   ```

4. **验证部署**
   - 检查API健康状态
   - 测试支付流程
   - 验证webhook接收

#### 部署后检查
- [ ] 所有API端点正常响应
- [ ] Stripe支付流程正常
- [ ] Creem支付流程正常（如果启用）
- [ ] Webhook接收正常
- [ ] 数据库查询性能正常
- [ ] 错误监控正常

### 4. 监控和告警

#### 关键指标监控
```typescript
// utils/monitoring.ts
export const PaymentMetrics = {
  // 支付成功率
  paymentSuccessRate: (provider: string) => {
    // 实现支付成功率计算
  },

  // API响应时间
  apiResponseTime: (endpoint: string) => {
    // 实现响应时间监控
  },

  // 错误率
  errorRate: (provider: string) => {
    // 实现错误率计算
  }
};
```

#### 告警配置
```yaml
# alerts.yml
alerts:
  - name: payment_success_rate_low
    condition: payment_success_rate < 95%
    severity: critical

  - name: api_response_time_high
    condition: api_response_time > 5s
    severity: warning

  - name: payment_provider_down
    condition: provider_error_rate > 50%
    severity: critical
```

## 🔍 故障排查

### 1. 常见问题

#### Stripe相关问题
- **问题**: Stripe webhook验证失败
- **解决**: 检查webhook密钥配置，确保请求体未被修改

- **问题**: 支付会话创建失败
- **解决**: 验证API密钥权限，检查参数格式

#### Creem相关问题
- **问题**: API认证失败
- **解决**: 检查`x-api-key`头部设置，确认API密钥有效

- **问题**: 支付跳转失败
- **解决**: 验证回调URL配置，检查域名白名单

#### 通用问题
- **问题**: 数据库连接失败
- **解决**: 检查数据库连接字符串，验证网络连通性

- **问题**: 环境变量未加载
- **解决**: 确认.env文件位置，检查变量名拼写

### 2. 日志分析

```typescript
// utils/logger.ts
export const PaymentLogger = {
  logPaymentAttempt: (provider: string, orderNo: string, amount: number) => {
    console.log(`[PAYMENT] Attempt: ${provider} | Order: ${orderNo} | Amount: ${amount}`);
  },

  logPaymentSuccess: (provider: string, orderNo: string, sessionId: string) => {
    console.log(`[PAYMENT] Success: ${provider} | Order: ${orderNo} | Session: ${sessionId}`);
  },

  logPaymentError: (provider: string, error: string, context?: any) => {
    console.error(`[PAYMENT] Error: ${provider} | ${error}`, context);
  }
};
```

### 3. 性能优化

#### 缓存策略
```typescript
// utils/cache.ts
export const PaymentCache = {
  // 缓存支付平台配置
  cacheProviderConfig: (provider: string, config: any) => {
    // 实现配置缓存
  },

  // 缓存用户支付偏好
  cacheUserPreference: (userId: string, provider: string) => {
    // 实现用户偏好缓存
  }
};
```

#### 数据库优化
```sql
-- 优化查询索引
CREATE INDEX CONCURRENTLY idx_orders_user_provider ON orders(user_uuid, payment_provider);
CREATE INDEX CONCURRENTLY idx_orders_status_created ON orders(status, created_at);

-- 分析查询性能
EXPLAIN ANALYZE SELECT * FROM orders WHERE payment_provider = 'stripe' AND status = 'created';
```

## 🔒 安全考虑

### 1. API密钥管理

#### 密钥存储
```typescript
// utils/security.ts
export class SecureConfig {
  private static validateApiKey(key: string, provider: string): boolean {
    const patterns = {
      stripe: /^sk_(test_|live_)[a-zA-Z0-9]{24,}$/,
      creem: /^creem_(test_|live_)[a-zA-Z0-9]{24,}$/
    };

    return patterns[provider]?.test(key) || false;
  }

  static getProviderConfig(provider: string) {
    const config = {
      stripe: {
        publicKey: process.env.STRIPE_PUBLIC_KEY,
        privateKey: process.env.STRIPE_PRIVATE_KEY,
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET
      },
      creem: {
        apiKey: process.env.CREEM_API_KEY,
        webhookSecret: process.env.CREEM_WEBHOOK_SECRET
      }
    };

    const providerConfig = config[provider];
    if (!providerConfig) {
      throw new Error(`Unknown provider: ${provider}`);
    }

    // 验证密钥格式
    Object.entries(providerConfig).forEach(([key, value]) => {
      if (value && !this.validateApiKey(value, provider)) {
        console.warn(`Invalid ${provider} ${key} format`);
      }
    });

    return providerConfig;
  }
}
```

#### 环境隔离
```bash
# 开发环境密钥前缀
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_PRIVATE_KEY="sk_test_..."
CREEM_API_KEY="creem_test_..."

# 生产环境密钥前缀
STRIPE_PUBLIC_KEY="pk_live_..."
STRIPE_PRIVATE_KEY="sk_live_..."
CREEM_API_KEY="creem_live_..."
```

### 2. Webhook安全

#### 签名验证
```typescript
// services/payment/security.ts
export class WebhookSecurity {
  static verifyStripeSignature(body: string, signature: string, secret: string): boolean {
    try {
      const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY!);
      stripe.webhooks.constructEvent(body, signature, secret);
      return true;
    } catch (error) {
      console.error('Stripe signature verification failed:', error);
      return false;
    }
  }

  static verifyCreemSignature(body: string, signature: string, secret: string): boolean {
    try {
      // 实现Creem签名验证逻辑
      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(body)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
      );
    } catch (error) {
      console.error('Creem signature verification failed:', error);
      return false;
    }
  }

  static verifyWebhook(provider: string, body: string, signature: string): boolean {
    const config = SecureConfig.getProviderConfig(provider);

    switch (provider) {
      case 'stripe':
        return this.verifyStripeSignature(body, signature, config.webhookSecret);
      case 'creem':
        return this.verifyCreemSignature(body, signature, config.webhookSecret);
      default:
        return false;
    }
  }
}
```

#### IP白名单
```typescript
// middleware/webhook-security.ts
const WEBHOOK_IPS = {
  stripe: [
    '**********',
    '*************',
    '*************',
    // ... 更多Stripe IP
  ],
  creem: [
    // Creem的webhook IP地址
  ]
};

export function validateWebhookIP(provider: string, clientIP: string): boolean {
  const allowedIPs = WEBHOOK_IPS[provider];
  return allowedIPs ? allowedIPs.includes(clientIP) : false;
}
```

### 3. 数据加密

#### 敏感数据加密
```typescript
// utils/encryption.ts
import crypto from 'crypto';

export class DataEncryption {
  private static readonly algorithm = 'aes-256-gcm';
  private static readonly keyLength = 32;

  static encrypt(text: string, key: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, key);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  static decrypt(encryptedData: string, key: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];

    const decipher = crypto.createDecipher(this.algorithm, key);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
```

### 4. 访问控制

#### API限流
```typescript
// middleware/rate-limit.ts
import { NextRequest } from 'next/server';

const rateLimitMap = new Map();

export function rateLimit(identifier: string, limit: number, windowMs: number): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;

  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, []);
  }

  const requests = rateLimitMap.get(identifier);

  // 清理过期请求
  const validRequests = requests.filter(time => time > windowStart);

  if (validRequests.length >= limit) {
    return false; // 超出限制
  }

  validRequests.push(now);
  rateLimitMap.set(identifier, validRequests);

  return true; // 允许请求
}

export function applyRateLimit(req: NextRequest) {
  const ip = req.ip || 'unknown';
  const isAllowed = rateLimit(ip, 100, 60000); // 每分钟100次请求

  if (!isAllowed) {
    throw new Error('Rate limit exceeded');
  }
}
```

## 📋 最佳实践

### 1. 代码组织

#### 目录结构
```
services/
├── payment/
│   ├── base.ts          # 抽象接口
│   ├── factory.ts       # 工厂类
│   ├── stripe.ts        # Stripe实现
│   ├── creem.ts         # Creem实现
│   ├── types.ts         # 类型定义
│   └── security.ts      # 安全工具
├── order.ts             # 订单服务
└── credit.ts            # 积分服务

app/api/
├── checkout/
│   └── route.ts         # 统一支付入口
├── payment-notify/
│   └── [provider]/
│       └── route.ts     # 通用webhook处理
└── stripe-notify/
    └── route.ts         # Stripe专用webhook（兼容）

components/
├── payment/
│   ├── PaymentProviderSelector.tsx
│   ├── PaymentMethodCard.tsx
│   └── PaymentStatus.tsx
└── shared/
    └── PricingContent.tsx
```

#### 命名规范
- **函数命名**: 使用动词开头，明确表达功能
  - `createStripeSession()` → `createCheckoutSession()`
  - `handleStripeWebhook()` → `handleWebhook()`

- **变量命名**: 使用描述性名称
  - `session_id` → `paymentSessionId`
  - `provider` → `paymentProvider`

- **常量命名**: 使用大写字母和下划线
  - `STRIPE_CONFIG` → `PAYMENT_PROVIDER_CONFIG`

### 2. 错误处理

#### 统一错误类型
```typescript
// types/errors.ts
export class PaymentError extends Error {
  constructor(
    message: string,
    public provider: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'PaymentError';
  }
}

export class WebhookError extends Error {
  constructor(
    message: string,
    public provider: string,
    public signature?: string
  ) {
    super(message);
    this.name = 'WebhookError';
  }
}
```

#### 错误处理中间件
```typescript
// middleware/error-handler.ts
export function handlePaymentError(error: Error, provider: string) {
  if (error instanceof PaymentError) {
    console.error(`Payment error [${provider}]:`, {
      message: error.message,
      code: error.code,
      details: error.details
    });
  } else {
    console.error(`Unexpected error [${provider}]:`, error);
  }

  // 发送错误监控
  // sendErrorToMonitoring(error, { provider });
}
```

### 3. 性能优化

#### 连接池管理
```typescript
// utils/connection-pool.ts
export class PaymentConnectionPool {
  private static pools: Map<string, any> = new Map();

  static getConnection(provider: string) {
    if (!this.pools.has(provider)) {
      const pool = this.createPool(provider);
      this.pools.set(provider, pool);
    }

    return this.pools.get(provider);
  }

  private static createPool(provider: string) {
    // 根据provider创建相应的连接池
    switch (provider) {
      case 'stripe':
        return new Stripe(process.env.STRIPE_PRIVATE_KEY!, {
          maxNetworkRetries: 3,
          timeout: 10000
        });
      case 'creem':
        // 创建Creem连接池
        return null;
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }
}
```

#### 缓存策略
```typescript
// utils/payment-cache.ts
export class PaymentCache {
  private static cache: Map<string, any> = new Map();
  private static ttl: Map<string, number> = new Map();

  static set(key: string, value: any, ttlMs: number = 300000) { // 5分钟默认TTL
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttlMs);
  }

  static get(key: string): any | null {
    const expiry = this.ttl.get(key);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key);
      this.ttl.delete(key);
      return null;
    }

    return this.cache.get(key);
  }

  static cacheProviderConfig(provider: string, config: any) {
    this.set(`provider_config_${provider}`, config, 600000); // 10分钟
  }

  static getCachedProviderConfig(provider: string) {
    return this.get(`provider_config_${provider}`);
  }
}
```

### 4. 监控和日志

#### 结构化日志
```typescript
// utils/payment-logger.ts
export class PaymentLogger {
  static logPaymentEvent(event: string, data: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      service: 'payment',
      ...data
    };

    console.log(JSON.stringify(logEntry));
  }

  static logCheckoutAttempt(provider: string, orderNo: string, amount: number) {
    this.logPaymentEvent('checkout_attempt', {
      provider,
      orderNo,
      amount,
      level: 'info'
    });
  }

  static logPaymentSuccess(provider: string, orderNo: string, sessionId: string) {
    this.logPaymentEvent('payment_success', {
      provider,
      orderNo,
      sessionId,
      level: 'info'
    });
  }

  static logPaymentError(provider: string, error: string, context?: any) {
    this.logPaymentEvent('payment_error', {
      provider,
      error,
      context,
      level: 'error'
    });
  }
}
```

#### 性能监控
```typescript
// utils/performance-monitor.ts
export class PerformanceMonitor {
  private static timers: Map<string, number> = new Map();

  static startTimer(operation: string): string {
    const timerId = `${operation}_${Date.now()}_${Math.random()}`;
    this.timers.set(timerId, performance.now());
    return timerId;
  }

  static endTimer(timerId: string, operation: string) {
    const startTime = this.timers.get(timerId);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.timers.delete(timerId);

      PaymentLogger.logPaymentEvent('performance_metric', {
        operation,
        duration,
        level: 'info'
      });

      // 如果操作时间过长，记录警告
      if (duration > 5000) { // 5秒
        PaymentLogger.logPaymentEvent('slow_operation', {
          operation,
          duration,
          level: 'warning'
        });
      }
    }
  }

  static async measureAsync<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const timerId = this.startTimer(operation);
    try {
      const result = await fn();
      this.endTimer(timerId, operation);
      return result;
    } catch (error) {
      this.endTimer(timerId, operation);
      throw error;
    }
  }
}
```

## 🎯 总结

### 重构收益
1. **可扩展性**: 轻松添加新的支付平台
2. **可维护性**: 代码结构清晰，职责分离
3. **可靠性**: 统一的错误处理和监控
4. **安全性**: 完善的安全措施和验证机制
5. **性能**: 优化的缓存和连接管理

### 关键成功因素
1. **渐进式迁移**: 保持向后兼容，分阶段实施
2. **充分测试**: 单元测试、集成测试、E2E测试全覆盖
3. **监控告警**: 实时监控支付流程，快速发现问题
4. **文档维护**: 保持文档更新，便于团队协作
5. **安全第一**: 严格的安全措施，保护用户数据

### 后续优化方向
1. **智能路由**: 根据用户偏好和成功率自动选择支付平台
2. **A/B测试**: 测试不同支付平台的转化率
3. **国际化**: 支持更多地区的本地支付方式
4. **移动优化**: 针对移动端优化支付体验
5. **数据分析**: 深入分析支付数据，优化业务策略

---

*本文档提供了完整的多支付平台重构实施指南，涵盖了从架构设计到部署上线的全流程，确保重构项目的成功实施*
