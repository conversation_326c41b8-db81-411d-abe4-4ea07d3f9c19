import NextAuth from "next-auth";
import { authOptions } from "./config";



export const { handlers, signIn, signOut, auth } = NextAuth(authOptions);



// 🎉 登录问题修复总结
// 问题根源
// 提交 d261648 ("try 未登录弹出登录框；谷歌改弹窗而不是直接跳转") 引入了错误的登录处理方式。

// 关键修复步骤
// 🔑 修复 AUTH_SECRET

// 问题: 原密钥包含可读文本 "james"，不符合 NextAuth.js 要求
// 解决: 生成新的 32 字符随机密钥
// # 原来: "Zt3BXVudzzRq2R2WBjames1dNMq48Gg9zKAYq7YwSL0="
// # 修复: "TGZXB3DeRFcGvdlwJhtFiOJEfASrxon1Ck2YL2oyCgk="
// 📝 修复环境变量格式

// 问题: 等号两边有空格
// 解决: 移除空格 (AUTH_SECRET = "xxx" → AUTH_SECRET="xxx")
// 🔄 恢复正确的登录处理

// 问题: 使用了错误的 openPopupSignIn 函数直接访问 /api/auth/signin/google
// 解决: 恢复使用 NextAuth 的 signIn("google") 函数
// ⚙️ 优化 Google Provider 配置

// 问题: Token 交换阶段网络连接问题
// 解决: 添加明确的 scope 配置