'use client';

import { But<PERSON> } from "@/components/ui/button";
import { useModalStore } from "@/stores/modalStore";

export default function ModalTestButtons() {
  const openModal = useModalStore((state) => state.openModal);

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 p-4 bg-white border rounded-lg shadow-lg">
      <h3 className="text-sm font-semibold text-gray-700 mb-2">Modal Tests</h3>
      <Button
        onClick={() => openModal('my-orders', { locale: 'en' })}
        variant="outline"
        size="sm"
      >
        Test My Orders
      </Button>
      <Button
        onClick={() => openModal('my-credits', { locale: 'en' })}
        variant="outline"
        size="sm"
      >
        Test My Credits
      </Button>
      <Button
        onClick={() => openModal('my-invites', { locale: 'en' })}
        variant="outline"
        size="sm"
      >
        Test My Invites
      </Button>
      <Button
        onClick={() => openModal('api-keys', { locale: 'en' })}
        variant="outline"
        size="sm"
      >
        Test API Keys
      </Button>
    </div>
  );
}