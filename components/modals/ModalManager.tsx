// components/modals/ModalManager.tsx
'use client';

import { useModalStore, ModalType } from '@/stores/modalStore';
import dynamic from 'next/dynamic';

// 使用 next/dynamic 对所有 Modal 组件进行懒加载
// 这能确保 Modal 的代码不会被打包进初始的 JS bundle 中
const modalComponents: Record<ModalType, React.ComponentType<any>> = {
  login: dynamic(() => import('./LoginModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  'pricing-modal': dynamic(() => import('./PricingModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  feedback: dynamic(() => import('./FeedbackModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  aiModel: dynamic(() => import('./AIModelModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  userProfile: dynamic(() => import('./UserProfileModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  settings: dynamic(() => import('./SettingsModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  'my-orders': dynamic(() => import('./MyOrdersModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  'my-credits': dynamic(() => import('./MyCreditsModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  'my-invites': dynamic(() => import('./MyInvitesModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  'api-keys': dynamic(() => import('./ApiKeysModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
  'my-works': dynamic(() => import('./MyWorksModal'), {
    loading: () => <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
    </div>
  }),
};

export default function ModalManager() {
  // 从 store 中订阅当前激活的 Modal 状态
  // 只有当 activeModal 变化时，这个组件才会重渲染
  const activeModal = useModalStore((state) => state.activeModal);

  // 如果没有激活的 Modal，则不渲染任何东西
  if (!activeModal) {
    return null;
  }

  // 根据 activeModal.type 从映射表中找到对应的组件
  const ActiveModalComponent = modalComponents[activeModal.type];

  // 渲染激活的 Modal，并将 store 中存的 props 传递给它
  return <ActiveModalComponent {...activeModal.props} />;
}
