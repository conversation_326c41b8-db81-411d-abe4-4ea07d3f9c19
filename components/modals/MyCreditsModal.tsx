'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { format } from "date-fns";
import TableSlot from "@/components/console/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";

interface MyCreditsModalProps {
  locale?: string;
}

export default function MyCreditsModal({
  locale = 'en'
}: MyCreditsModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const t = useTranslations();

  const [credits, setCredits] = useState([]);
  const [userCredits, setUserCredits] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadCredits = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/get-user-info');
        const result = await response.json();
        if (result.data && result.data.creditsHistory) {
          setCredits(result.data.creditsHistory);
        }
        if (result.data && result.data.userCredits) {
          setUserCredits(result.data.userCredits);
        }
      } catch (error) {
        console.error('Failed to load credits:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCredits();
  }, []);

  const table: TableSlotType = {
    title: t("my_credits.title"),
    tip: {
      title: t("my_credits.left_tip", {
        left_credits: userCredits?.left_credits || 0,
      }),
    },
    toolbar: {
      items: [
        {
          title: t("my_credits.recharge"),
          url: "/pricing",
          target: "_blank",
          icon: "RiBankCardLine",
        },
      ],
    },
    columns: [
      {
        title: t("my_credits.table.trans_no"),
        name: "trans_no",
      },
      {
        title: t("my_credits.table.trans_type"),
        name: "trans_type",
      },
      {
        title: t("my_credits.table.credits"),
        name: "credits",
      },
      {
        title: t("my_credits.table.updated_at"),
        name: "created_at",
        callback: (v: any) => {
          return format(new Date(v.created_at), "yyyy-MM-dd HH:mm:ss");
        },
      },
    ],
    data: credits,
    empty_message: t("my_credits.no_credits"),
  };

  const content = (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading...</span>
        </div>
      ) : (
        <TableSlot {...table} />
      )}
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t("my_credits.title")}</DialogTitle>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent className="max-h-[95vh]">
        <DrawerHeader className="text-left">
          <DrawerTitle>{t("my_credits.title")}</DrawerTitle>
        </DrawerHeader>
        <div className="px-4 overflow-y-auto flex-1">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              Close
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}