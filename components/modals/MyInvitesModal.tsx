'use client';

import { useModalStore } from '@/stores/modalStore';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/useMediaQuery";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { format } from "date-fns";
import { TableColumn } from "@/types/blocks/table";
import TableBlock from "@/components/blocks/table";
import { Table as TableSlotType } from "@/types/slots/table";
import Invite from "@/components/invite";

interface MyInvitesModalProps {
  locale?: string;
}

export default function MyInvitesModal({
  locale = 'en'
}: MyInvitesModalProps) {
  const closeModal = useModalStore((state) => state.closeModal);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const t = useTranslations();

  const [affiliates, setAffiliates] = useState([]);
  const [summary, setSummary] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadInvites = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/get-user-info');
        const result = await response.json();
        if (result.data && result.data.affiliates) {
          setAffiliates(result.data.affiliates);
        }
        if (result.data && result.data.affiliateSummary) {
          setSummary(result.data.affiliateSummary);
        }
      } catch (error) {
        console.error('Failed to load invites:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInvites();
  }, []);

  const columns: TableColumn[] = [
    {
      name: "created_at",
      title: t("my_invites.table.invite_time"),
      callback: (item) => format(new Date(item.created_at), "yyyy-MM-dd HH:mm:ss"),
    },
    {
      name: "user",
      title: t("my_invites.table.invite_user"),
      callback: (item) => (
        <div className="flex items-center gap-2">
          {item?.user?.avatar_url && (
            <img
              src={item.user?.avatar_url || ""}
              className="w-8 h-8 rounded-full"
            />
          )}
          <span>{item.user?.nickname}</span>
        </div>
      ),
    },
    {
      name: "status",
      title: t("my_invites.table.status"),
      callback: (item) =>
        item.status === "pending"
          ? t("my_invites.table.pending")
          : t("my_invites.table.completed"),
    },
    {
      name: "reward_amount",
      title: t("my_invites.table.reward_amount"),
      callback: (item) => `${item.reward_amount / 100}`,
    },
  ];

  const table: TableSlotType = {
    title: t("my_invites.title"),
    description: t("my_invites.description"),
    tip: {
      description: t("my_invites.my_invite_link"),
    },
    columns: columns,
    data: affiliates,
    empty_message: t("my_invites.no_invites"),
  };

  const content = (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading...</span>
        </div>
      ) : (
        <div className="space-y-8">
          {summary && <Invite summary={summary} />}
          <TableBlock {...table} />
        </div>
      )}
    </div>
  );

  if (isDesktop) {
    return (
      <Dialog open={true} onOpenChange={(open) => !open && closeModal()}>
        <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{t("my_invites.title")}</DialogTitle>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={true} onOpenChange={(open) => !open && closeModal()}>
      <DrawerContent className="max-h-[95vh]">
        <DrawerHeader className="text-left">
          <DrawerTitle>{t("my_invites.title")}</DrawerTitle>
        </DrawerHeader>
        <div className="px-4 overflow-y-auto flex-1">
          {content}
        </div>
        <DrawerFooter className="pt-4">
          <DrawerClose asChild>
            <Button variant="outline" onClick={closeModal}>
              Close
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}