"use client";

import { useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";

// 导入响应式样式hooks
import { useCardStyles } from "./hooks/use-responsive-styles";
import { useDeviceLayout } from "./hooks/use-device-layout";

// 导入拆分的组件
import {
  ModelSelector,
  OptionsConfig,
  CostEstimate,
  CreditsDisplay,
  GenerateButton,
  PromptInput
} from "./components";

// 导入新的动态配置组件
import { DynamicOptionsConfig, DynamicOptionsConfigRef } from "./components/input/DynamicOptionsConfig";

// 导入新的Context Provider和Hook
import { AIGenerationProvider, useAIGeneration } from "./components/hooks/useAIGeneration";

interface InputMainProps {
  modelType?: string;
  isFullscreen?: boolean;
  useDynamicConfig?: boolean;
}

export function InputMain({
  modelType,
  isFullscreen,
  useDynamicConfig = true
}: InputMainProps = {}) {
  // 创建 DynamicOptionsConfig 的引用
  const optionsConfigRef = useRef<DynamicOptionsConfigRef>(null);

  return (
    <AIGenerationProvider 
      modelType={modelType}
      optionsConfigRef={optionsConfigRef}
    >
      <AIFormContent 
        isFullscreen={isFullscreen}
        useDynamicConfig={useDynamicConfig}
        optionsConfigRef={optionsConfigRef}
      />
    </AIGenerationProvider>
  );
}

export function AIFormContent({ 
  isFullscreen, 
  useDynamicConfig = true,
  optionsConfigRef
}: {
  isFullscreen?: boolean;
  useDynamicConfig?: boolean;
  optionsConfigRef: React.RefObject<DynamicOptionsConfigRef>;
}) {
  const { state, form, handleGenerate, handleModelSelect } = useAIGeneration();
  
  // 使用响应式样式
  const { isMobile } = useDeviceLayout();
  const { className: cardClassName } = useCardStyles();

  return (
    <div className={`w-full max-w-full ${
      isFullscreen ? 'h-full overflow-x-hidden' : ''
    } ${isMobile ? 'space-y-1' : 'space-y-6'}`}>
      <Card className={`${cardClassName} bg-gradient-to-br from-card via-card to-accent/5 w-full max-w-full ${
        isFullscreen ? 'overflow-x-hidden overflow-y-hidden gap-0' : ''
      }`}>
        <CardContent className={`w-full max-w-full ${
          isFullscreen ? 'overflow-x-hidden' : ''
        } ${isMobile ? 'space-y-1 px-2 py-1' : 'space-y-4 px-6 py-4'}`}>
          {/* 模型选择组件 */}
          <ModelSelector
            selectedModel={state.selectedModel}
            models={state.models}
            modelsLoading={state.modelsLoading}
            modelsError={state.modelsError}
            onModelSelect={handleModelSelect}
          />

          {/* 提示词输入组件 */}
          <PromptInput />

          {/* 选项配置组件 */}
          {useDynamicConfig ? (
            <DynamicOptionsConfig
              ref={optionsConfigRef}
              selectedModel={state.selectedModel}
              options={state.formData.options}
              onOptionsChange={(options) => form.setValue('options', options as any)}
              modelsLoading={state.modelsLoading}
            />
          ) : (
            <OptionsConfig
              selectedModel={state.selectedModel}
              options={state.formData.options}
              onOptionsChange={(options) => form.setValue('options', options as any)}
            />
          )}

          {/* 成本预估组件 */}
          <CostEstimate costEstimate={state.costEstimate} />

          {/* 积分显示组件 */}
          <CreditsDisplay userCredits={state.userCredits} />

          {/* 生成按钮组件 */}
          <GenerateButton
            loading={state.loading}
            selectedModel={state.selectedModel}
            prompt={state.formData.prompt}
            costEstimate={state.costEstimate}
            onGenerate={handleGenerate}
          />
        </CardContent>
      </Card>
    </div>
  );
}
