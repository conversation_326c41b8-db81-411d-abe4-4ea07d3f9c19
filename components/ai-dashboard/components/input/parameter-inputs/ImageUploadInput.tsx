"use client";

import { useState, useRef, useEffect, useImper<PERSON><PERSON><PERSON>le, forwardRef } from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Upload, X, HelpCircle, Clock } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/services/provider/types";

// 图片项类型定义
interface ImageItem {
  id: string;
  type: 'local' | 'uploaded';
  url: string; // 对于local类型是base64，对于uploaded类型是HTTP URL
  filename: string;
  contentType: string;
  size: number;
  originalFile?: File; // 保存原始文件用于大文件上传
}

// 批量上传方法接口
export interface ImageUploadInputRef {
  uploadPendingImages: () => Promise<string[]>;
  hasPendingImages: () => boolean;
}

// 批量上传响应类型
interface BatchUploadResult {
  success: boolean;
  url?: string;
  filename?: string;
  key?: string;
  size?: number;
  type?: string;
  index: number;
  error?: string;
}

interface BatchUploadResponse {
  code: number;
  msg?: string;
  data: {
    results: BatchUploadResult[];
    summary: {
      total: number;
      success: number;
      failed: number;
    };
  };
}

// 单文件上传响应类型
interface SingleUploadResponse {
  code: number;
  msg?: string;
  data: {
    url: string;
    filename: string;
    key: string;
    size: number;
    type: string;
  };
}

interface ImageUploadInputProps {
  config: ParameterConfig;
  value: string[] | undefined;
  onChange: (value: string[]) => void;
  error?: string;
  modelType?: string;
}

export const ImageUploadInput = forwardRef<ImageUploadInputRef, ImageUploadInputProps>(({
  config,
  value,
  onChange,
  error,
  modelType = 'image'
}, ref) => {
  const t = useTranslations("ai-dashboard");
  const { isMobile, isSmallMobile } = useDeviceLayout();

  // 新的状态管理：维护图片项数组
  const [imageItems, setImageItems] = useState<ImageItem[]>([]);
  const [uploading, setUploading] = useState(false);
  const [batchUploading, setBatchUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // 暴露批量上传方法给父组件
  useImperativeHandle(ref, () => ({
    uploadPendingImages: async () => {
      console.log('[ImageUpload] uploadPendingImages called, imageItems:', imageItems.map(item => ({
        id: item.id,
        type: item.type,
        filename: item.filename,
        size: item.size,
        hasOriginalFile: !!item.originalFile
      })));

      const localImages = imageItems.filter(item => item.type === 'local');
      if (localImages.length === 0) {
        console.log('[ImageUpload] No local images found, returning existing URLs');
        return imageItems.map(item => item.url);
      }

      console.log('[ImageUpload] Starting mixed upload strategy for', localImages.length, 'local images');
      setBatchUploading(true);
      try {
        // 文件大小阈值：3MB
        const FILE_SIZE_THRESHOLD = 3 * 1024 * 1024;

        // 分离小文件和大文件
        const smallFiles = localImages.filter(item => item.size < FILE_SIZE_THRESHOLD);
        const largeFiles = localImages.filter(item => item.size >= FILE_SIZE_THRESHOLD);

        const finalUrls: string[] = [];

        // 处理小文件：直接返回base64（发送给provider）
        for (const item of smallFiles) {
          finalUrls.push(item.url); // base64数据
          console.log(`[ImageUpload] Small file (${item.size} bytes) using base64 strategy:`, item.filename);
        }

        // 处理大文件：上传到R2获取URL
        for (const item of largeFiles) {
          if (item.originalFile) {
            try {
              const formData = new FormData();
              formData.append('file', item.originalFile);

              const response = await fetch('/api/ai/upload-image', {
                method: 'POST',
                body: formData
              });

              const result = await response.json() as SingleUploadResponse;
              if (result.code === 0) {
                finalUrls.push(result.data.url);
                console.log(`[ImageUpload] Large file (${item.size} bytes) uploaded to R2:`, result.data.url);
              } else {
                throw new Error(`Upload failed for ${item.filename}: ${result.msg}`);
              }
            } catch (error) {
              console.error(`Failed to upload large file ${item.filename}:`, error);
              throw error;
            }
          }
        }

        // 更新状态：将所有本地图片标记为已处理
        const updatedItems = imageItems.map(item => {
          if (item.type === 'local') {
            const finalUrl = finalUrls[localImages.findIndex(li => li.id === item.id)];
            return {
              ...item,
              type: 'uploaded' as const,
              url: finalUrl || item.url
            };
          }
          return item;
        });

        setImageItems(updatedItems);
        onChange(finalUrls);

        console.log(`[ImageUpload] Mixed strategy completed: ${smallFiles.length} base64, ${largeFiles.length} URLs`);
        return finalUrls;

      } catch (error) {
        console.error('Mixed upload strategy error:', error);
        throw error;
      } finally {
        setBatchUploading(false);
      }
    },
    hasPendingImages: () => {
      const hasPending = imageItems.some(item => item.type === 'local');
      console.log('[ImageUpload] hasPendingImages check:', {
        imageItemsCount: imageItems.length,
        localItems: imageItems.filter(item => item.type === 'local').length,
        hasPending
      });
      return hasPending;
    }
  }), [imageItems, onChange]);

  // 同步外部值变化到本地状态
  useEffect(() => {
    if (value) {
      // 将外部传入的URL转换为ImageItem格式
      const urlItems: ImageItem[] = value.map((url, index) => ({
        id: `external-${index}-${Date.now()}`,
        type: 'uploaded',
        url,
        filename: `image-${index + 1}.jpg`,
        contentType: 'image/jpeg',
        size: 0 // 外部URL无法确定大小
      }));
      setImageItems(urlItems);
    } else {
      setImageItems([]);
    }
  }, [value]);

  // 防止页面默认的拖放行为
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault();
    };

    const handlePageDrop = (e: DragEvent) => {
      e.preventDefault();
      if (!dropZoneRef.current?.contains(e.target as Node)) {
        return false;
      }
    };

    document.addEventListener('dragover', preventDefault);
    document.addEventListener('drop', handlePageDrop);

    return () => {
      document.removeEventListener('dragover', preventDefault);
      document.removeEventListener('drop', handlePageDrop);
    };
  }, []);

  // 处理文件预览（统一转base64预览，保存原始文件）
  const processFileForPreview = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert(t("errors.invalid_input"));
      return;
    }

    // 验证文件大小 (最大 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      alert("文件大小超过限制，最大支持10MB");
      return;
    }

    setUploading(true);
    try {
      // 转换为 base64 用于本地预览
      const base64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      // 创建新的图片项（保存原始文件用于后续智能上传）
      const newImageItem: ImageItem = {
        id: `local-${Date.now()}-${Math.random().toString(36).substring(2)}`,
        type: 'local',
        url: base64,
        filename: file.name,
        contentType: file.type,
        size: file.size,
        originalFile: file // 保存原始文件
      };

      // 更新状态
      const updatedItems = [...imageItems, newImageItem];
      setImageItems(updatedItems);

      // 通知父组件（传递base64用于预览）
      const allUrls = updatedItems.map(item => item.url);
      onChange(allUrls);

      console.log('[ImageUpload] Added image for preview:', {
        id: newImageItem.id,
        filename: newImageItem.filename,
        size: newImageItem.size,
        url: newImageItem.url.substring(0, 50) + '...'
      });
    } catch (error) {
      console.error('File processing error:', error);
      alert(t("errors.network_error"));
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await processFileForPreview(file);
  };

  // 拖放事件处理函数
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev + 1);

    if (e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => {
      const newCount = prev - 1;
      if (newCount === 0) {
        setIsDragOver(false);
      }
      return newCount;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.types.includes('Files')) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragOver(false);
    setDragCounter(0);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert(t("errors.invalid_input"));
      return;
    }

    if (imageFiles.length > 1) {
      alert(`检测到 ${imageFiles.length} 个图片文件，将处理第一个：${imageFiles[0].name}`);
    }

    await processFileForPreview(imageFiles[0]);
  };

  const removeImage = (index: number) => {
    const updatedItems = imageItems.filter((_, i) => i !== index);
    setImageItems(updatedItems);
    const allUrls = updatedItems.map(item => item.url);
    onChange(allUrls);
  };

  // 根据模型类型确定标签文本
  const getLabelText = () => {
    switch (modelType) {
      case 'video':
        return t("options.first_frame");
      case 'image':
        return t("options.reference_image");
      default:
        return config.description || t("options.image_upload");
    }
  };

  return (
    <div className="space-y-2">
      {/* 标签和帮助图标 */}
      <div className="flex items-center gap-2">
        <Label 
          className={`font-medium text-foreground ${
            isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
          }`}
        >
          {getLabelText()}
          {config.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {config.tooltip && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className={`text-muted-foreground cursor-help ${
                  isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                }`} />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{config.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* 拖放上传区域 */}
      <div
        ref={dropZoneRef}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        className={`
          relative border-2 border-dashed rounded-xl transition-all duration-300 cursor-pointer
          ${isDragOver
            ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg shadow-primary/20'
            : 'border-border/50 hover:border-border hover:bg-muted/20'
          }
          ${uploading || batchUploading ? 'pointer-events-none opacity-50' : ''}
          ${error ? 'border-destructive/50' : ''}
          ${isSmallMobile ? 'p-4' : 'p-6'}
          group
        `}
        onClick={() => !uploading && !batchUploading && fileInputRef.current?.click()}
      >
        <div className="flex flex-col items-center justify-center gap-3 text-center">
          <div className={`
            rounded-full transition-all duration-300
            ${isDragOver
              ? 'bg-primary text-primary-foreground scale-110 animate-pulse'
              : 'bg-muted group-hover:bg-muted/80'
            }
            ${isSmallMobile ? 'p-2' : 'p-3'}
          `}>
            <Upload className={`text-current transition-transform duration-300 ${
              isDragOver ? 'scale-110' : 'group-hover:scale-100'
            } ${isSmallMobile ? 'w-5 h-5' : 'w-6 h-6'}`} />
          </div>

          <div className="space-y-1">
            <p className={`font-medium transition-colors duration-300 ${
              isDragOver ? 'text-primary' : 'text-foreground'
            } ${isSmallMobile ? 'text-sm' : 'text-base'}`}>
              {batchUploading ? "批量上传中..." :
               uploading ? "处理中..." :
               isDragOver ? t("options.drop_to_upload") : t("options.drag_drop")}
            </p>
            <p className={`text-muted-foreground ${
              isSmallMobile ? 'text-xs' : 'text-sm'
            }`}>
              {t("options.supported_formats")}
            </p>
            {isDragOver && (
              <p className={`text-primary font-medium animate-bounce ${
                isSmallMobile ? 'text-xs' : 'text-sm'
              }`}>
                {t("options.file_detected")}
              </p>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>

      {/* 图片预览 */}
      {imageItems.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
          {imageItems.map((imageItem, index) => (
            <div key={imageItem.id} className="relative group">
              <img
                src={imageItem.url}
                alt={`${getLabelText()} ${index + 1}`}
                className="w-full h-20 object-cover rounded-lg border border-border/50"
              />

              {/* 状态指示器 */}
              {imageItem.type === 'local' && (
                <div className="absolute top-1 left-1 bg-orange-500 text-white text-xs px-1 py-0.5 rounded flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  待处理
                </div>
              )}

              {/* 删除按钮 */}
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={() => removeImage(index)}
                className="absolute top-1 right-1 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}
    </div>
  );
});

ImageUploadInput.displayName = 'ImageUploadInput';
