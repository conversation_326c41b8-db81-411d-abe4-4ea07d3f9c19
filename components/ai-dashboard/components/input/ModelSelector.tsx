"use client";

import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  RichSelect,
  RichSelectContent,
  RichSelectItem,
  RichSelectTrigger,
  RichSelectValue,
  type RichSelectOption
} from "@/components/ui/rich-select";
import { useTranslations } from "next-intl";
import { getLocalizedContent } from "@/lib/i18n-content";
import type { AIModel } from "../types";
import { MODEL_TYPE_ICONS } from "../types";
import { useDeviceLayout } from "../../hooks/use-device-layout";  // 优化：导入设备布局hook

interface ModelSelectorProps {
  selectedModel: AIModel | null;
  models: AIModel[];
  modelsLoading: boolean;
  modelsError: string | null;
  onModelSelect: (modelId: string) => void;
}

export function ModelSelector({
  selectedModel,
  models,
  modelsLoading,
  modelsError,
  onModelSelect
}: ModelSelectorProps) {
  const t = useTranslations("ai-dashboard.models");
  const tCost = useTranslations("ai-dashboard.cost");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取设备状态

  // 判断是否需要应用白色滤镜（针对深色单色图标）
  const shouldApplyWhiteFilter = (modelName: string) => {
    // 已知需要白色滤镜的模型（主要是深色单色图标）
    const darkIconModels = ['gpt', 'flux', 'openai', 'claude', 'anthropic'];
    return darkIconModels.some(name => modelName.toLowerCase().includes(name));
  };

  // 统一的图标创建函数
  const createIcon = (model: AIModel) => {
    const iconSize = isSmallMobile ? 'w-6 h-6' : isMobile ? 'w-7 h-7' : 'w-8 h-8';

    if (model.icon) {
      if (model.icon.endsWith('.svg')) {
        const needsWhiteFilter = shouldApplyWhiteFilter(model.model_name);

        return (
          <div className={`${iconSize} rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center`}>
            <img
              src={model.icon}
              alt={model.model_name}
              className={`w-full h-full object-contain ${needsWhiteFilter ? 'brightness-0 invert' : ''}`}
              style={needsWhiteFilter ? { filter: 'brightness(0) invert(1)' } : {}}
            />
          </div>
        );
      } else {
        return (
          <img
            src={model.icon}
            alt={model.model_name}
            className={`${iconSize} rounded-full object-contain`}
          />
        );
      }
    } else {
      const Icon = MODEL_TYPE_ICONS[model.model_type as keyof typeof MODEL_TYPE_ICONS] || MODEL_TYPE_ICONS.text;
      return <Icon className={`${iconSize} ${isSmallMobile ? 'p-1' : 'p-1.5'} rounded-full bg-gradient-to-r from-primary to-accent text-white fill-white`} />;
    }
  };

  const convertModelToOption = (model: AIModel): RichSelectOption => {
    // Format credit information as badge
    const creditBadge = `${model.credits_per_unit} / ${model.unit_type} `;

    return {
      value: model.model_id,
      label: model.model_name,
      description: model.description, // 只显示纯描述，不包含价格
      badge: creditBadge, // 价格显示在标题旁边
      icon: createIcon(model),
    };
  };

  const getAllModelOptions = (): RichSelectOption[] => {
    return models.map(convertModelToOption);
  };

  return (
    <div className={`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full ${isSmallMobile ? 'px-1.5 py-1' :  // 优化：超小屏幕最小内边距
      isMobile ? 'px-2 py-1.5' : 'px-3 py-2'
      }`}>  {/* 修复：移除overflow-x-hidden避免滚动条问题 */}
      <Label htmlFor="model" className={`font-medium text-foreground ${isSmallMobile ? 'text-xs' :  // 优化：超小屏幕最小字体
        isMobile ? 'text-xs' : 'text-sm'
        }`}>{t("model_selector")}</Label>
      {modelsLoading ? (
        <div className={`flex items-center justify-center bg-gradient-to-r from-accent/10 to-primary/10 rounded-md border border-border/20 ${isSmallMobile ? 'p-2 mt-1' :  // 优化：超小屏幕减少内边距和间距
          isMobile ? 'p-3 mt-1' : 'p-4 mt-2'
          }`}>
          <Loader2 className={`animate-spin mr-2 ${isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />  {/* 优化：响应式加载图标 */}
          <span className={isSmallMobile ? 'text-xs' : 'text-sm'}>{t("loading")}</span>
        </div>
      ) : modelsError ? (
        <div className={`bg-gradient-to-r from-destructive/10 to-destructive/5 rounded-md border border-destructive/20 ${isSmallMobile ? 'p-2 mt-1' :  // 优化：超小屏幕减少内边距和间距
          isMobile ? 'p-3 mt-1' : 'p-4 mt-2'
          }`}>
          <div className={`text-destructive ${isSmallMobile ? 'text-xs' : 'text-sm'}`}>
            {t("error")}: {modelsError}
          </div>
        </div>
      ) : (
        <RichSelect value={selectedModel?.model_id || ""} onValueChange={onModelSelect}>
          <RichSelectTrigger
            size={isMobile ? "default" : "lg"}  // 优化：移动端使用默认尺寸
            className={`w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl ${isSmallMobile ? 'mt-1' : isMobile ? 'mt-1' : 'mt-2'  // 优化：减少上边距
              }`}
          >
            <RichSelectValue placeholder={t("select_model")}>
              {selectedModel && (
                <div className={`flex items-center justify-between w-full ${isSmallMobile ? 'gap-1' : 'gap-2'}`}>
                  <div className={`flex items-center ${isSmallMobile ? 'gap-1.5' : 'gap-2'} min-w-0 flex-1`}>
                    {createIcon(selectedModel)}
                    <div className={`flex flex-col items-start text-left min-w-0 flex-1 ${isSmallMobile ? 'gap-0' : 'gap-0.5'
                      }`}>
                      <span className={`font-semibold truncate w-full ${isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
                        }`}>{selectedModel.model_name}</span>
                      {!isSmallMobile && selectedModel.description && (
                        <span className={`font-medium text-muted-foreground truncate w-full ${isMobile ? 'text-xs' : 'text-xs'
                          }`}>
                          {selectedModel.description}
                        </span>
                      )}
                    </div>
                  </div>
                  {/* Credit badge for selected model */}
                  <div className="flex-shrink-0">
                    <span className={`inline-flex items-center rounded-full font-medium bg-gradient-to-r from-orange-200 to-amber-200 text-amber-700  ${isSmallMobile ? 'px-1.5 py-0.5 text-[10px]' : 'px-2 py-0.5 text-xs'
                      }`}>
                      {selectedModel.credits_per_unit}
                    </span>
                  </div>
                </div>
              )}
            </RichSelectValue>
          </RichSelectTrigger>
          <RichSelectContent className="z-[150] w-[var(--radix-select-trigger-width)]">
            {getAllModelOptions().map((option) => (
              <RichSelectItem
                key={option.value}
                value={option.value}
                option={option}
              />
            ))}
          </RichSelectContent>
        </RichSelect>
      )}
    </div>
  );
}
