"use client";

import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../hooks/use-device-layout";
import { useAIGeneration } from "../hooks/useAIGeneration";

export function PromptInput() {
  const t = useTranslations("ai-dashboard.generator");
  const { isMobile, isSmallMobile } = useDeviceLayout();
  const { form } = useAIGeneration();

  return (
    <div className={`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full ${
      isSmallMobile ? 'px-1.5 py-1' :
      isMobile ? 'px-2 py-1.5' : 'px-3 py-2'
    }`}>
      <Label htmlFor="prompt" className={`font-medium text-foreground ${
        isSmallMobile ? 'text-xs' :
        isMobile ? 'text-xs' : 'text-sm'
      }`}>{t("prompt_input")}</Label>
      <Textarea
        id="prompt"
        placeholder={t("prompt_placeholder")}
        {...form.register("prompt")}
        rows={isSmallMobile ? 2 : isMobile ? 3 : 4}
        className={`bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border resize-none rounded-xl w-full max-w-full ${
          isSmallMobile ? 'mt-0.5 text-xs' :
          isMobile ? 'mt-1 text-sm' : 'mt-2 text-base'
        }`}
      />
      {form.formState.errors.prompt && (
        <p className="text-sm text-destructive mt-1">
          {form.formState.errors.prompt.message}
        </p>
      )}
    </div>
  );
}
