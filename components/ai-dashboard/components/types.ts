import { MessageSquare, Image, Video, Zap } from "lucide-react";
import { z } from "zod";

export interface AIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  credits_per_unit: number;
  unit_type: string;
  description?: string; // 保留兼容性
  description_i18n?: Record<string, string>; // 新的多语言描述字段
  model_name_i18n?: Record<string, string>; // 新的多语言名称字段
  supported_features?: string[];
  icon?: string;
  translationKey?: string; // 翻译文件中的key
}

// Zod Schema 定义
export const aiFormSchema = z.object({
  modelType: z.enum(['text', 'image', 'video']).default('text'),
  modelId: z.string().min(1, '请选择模型'),
  prompt: z.string().min(1, '请输入提示词').max(2000, '提示词过长'),
  options: z.object({
    size: z.string().optional(),
    aspectRatio: z.string().optional(),
    variants: z.number().min(1).max(4).optional(),
    temperature: z.number().min(0).max(1).optional(),
    max_tokens: z.number().min(1).max(4000).optional(),
    cdn: z.string().optional(),
    uploadedImages: z.array(z.string()).optional(),
    referenceImages: z.array(z.string()).optional(),
    firstFrameUrl: z.string().optional(),
    // Replicate 特定选项
    output_quality: z.number().min(0).max(100).optional(),
    output_format: z.enum(['webp', 'jpg', 'png']).optional(),
    guidance: z.number().min(0).max(10).optional(),
    num_inference_steps: z.number().min(1).max(50).optional(),
    prompt_strength: z.number().min(0).max(1).optional(),
    disable_safety_checker: z.boolean().optional(),
    go_fast: z.boolean().optional(),
    megapixels: z.enum(['0.25', '1']).optional(),
    seed: z.number().optional(),
    image: z.string().optional(),
  }).optional()
});

export type AIFormData = z.infer<typeof aiFormSchema>;

export interface GenerationResult {
  id: string;
  task_id: string; // 统一的任务ID，用于前端查询
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

// 移除向后兼容的接口，直接使用Context架构

export interface GenerationOptions {
  size?: string;
  aspectRatio?: string;
  variants?: number;
  temperature?: number;
  max_tokens?: number;
  cdn?: string;
  uploadedImages?: string[];
  referenceImages?: string[];
  firstFrameUrl?: string;

  // Replicate 特定选项
  output_quality?: number;
  output_format?: string;
  guidance?: number;
  num_inference_steps?: number;
  prompt_strength?: number;
  disable_safety_checker?: boolean;
  go_fast?: boolean;
  megapixels?: string;
  seed?: number;
  image?: string; // Input image for img2img mode
}

export interface CostEstimate {
  cost_estimate: {
    estimated_credits: number;
  };
  user_credits: {
    can_afford: boolean;
    shortfall?: number;
  };
}

export const MODEL_TYPE_ICONS = {
  text: MessageSquare,
  image: Image,
  video: Video,
  multimodal: Zap
};

// 扩展现有的 GenerationResult 接口
export interface EnhancedGenerationResult extends GenerationResult {
  formData?: AIFormData;
  selectedModel?: AIModel;
  timestamp?: Date;
  creditsUsed?: number;
}

export interface WorkspaceGenerationResult {
  id: string;
  task_id: string; // 统一的任务ID，用于前端查询
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export interface ModelTypeConfig {
  value: string;
  label: string;
  icon: any;
  color: string;
}


