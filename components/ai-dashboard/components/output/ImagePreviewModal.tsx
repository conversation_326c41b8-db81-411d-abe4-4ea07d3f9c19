"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download, X, ZoomIn, ZoomOut, RotateCw } from "lucide-react";
import { useTranslations } from "next-intl";

interface ImagePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  imageAlt: string;
  onDownload: () => void;
}

export function ImagePreviewModal({
  isOpen,
  onClose,
  imageUrl,
  imageAlt,
  onDownload
}: ImagePreviewModalProps) {
  const t = useTranslations("ai-dashboard");
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.25));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const resetTransform = () => {
    setZoom(1);
    setRotation(0);
    setPosition({ x: 0, y: 0 });
  };

  // 重置状态当模态框关闭时
  const handleClose = () => {
    resetTransform();
    onClose();
  };

  // 拖动处理函数
  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom <= 1) return; // 只有放大时才能拖动

    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || zoom <= 1) return;

    setPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 鼠标离开时停止拖动
  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // 鼠标滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const step = 0.05
    const delta = e.deltaY > 0 ? -1*step : step; // 向下滚动缩小，向上滚动放大
    const newZoom = Math.min(Math.max(zoom + delta, 0.25), 3);
    
    if (newZoom !== zoom) {
      setZoom(newZoom);
      
      // 如果缩放到1或以下，重置位置
      if (newZoom <= 1) {
        setPosition({ x: 0, y: 0 });
      }
    }
  };

  // 键盘快捷键
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          handleClose();
          break;
        case '+':
        case '=':
          e.preventDefault();
          handleZoomIn();
          break;
        case '-':
          e.preventDefault();
          handleZoomOut();
          break;
        case 'r':
        case 'R':
          e.preventDefault();
          handleRotate();
          break;
        case '0':
          e.preventDefault();
          resetTransform();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        hideCloseButton={true}
        className="!max-w-[85vw] !max-h-[90vh] !w-auto !h-auto p-0 bg-black/95 border-border/30 rounded-xl"
        style={{ maxWidth: '85vw', maxHeight: '90vh', width: 'auto', height: 'auto' }}
      >
        <DialogHeader className="absolute top-0 left-0 right-0 z-10 p-4 bg-gradient-to-b from-black/80 to-transparent rounded-t-xl">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-white text-lg font-semibold truncate">
              {imageAlt}
            </DialogTitle>
            <div className="flex items-center gap-2">
              {/* 控制按钮 */}
              <Button
                size="sm"
                variant="ghost"
                onClick={handleZoomOut}
                className="text-white hover:bg-white/20"
                disabled={zoom <= 0.25}
              >
                <ZoomOut className="w-4 h-4" />
              </Button>
              <span className="text-white text-sm min-w-[3rem] text-center">
                {Math.round(zoom * 100)}%
              </span>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleZoomIn}
                className="text-white hover:bg-white/20"
                disabled={zoom >= 3}
              >
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleRotate}
                className="text-white hover:bg-white/20"
              >
                <RotateCw className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={resetTransform}
                className="text-white hover:bg-white/20"
              >
              {t("preview.reset")}
              </Button>
              <Button
                size="sm"
                variant="default"
                onClick={onDownload}
                className="bg-primary hover:bg-primary/90"
              >
                <Download className="w-4 h-4 mr-2" />
                {t("actions.download")}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={handleClose}
                className="text-white hover:bg-white/20"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* 图片容器 */}
        <div
          className="flex items-center justify-center min-h-[60vh] max-h-[90vh] overflow-hidden rounded-xl"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          onWheel={handleWheel}
        >
          <img
            ref={imageRef}
            src={imageUrl}
            alt={imageAlt}
            className="max-w-full max-h-full object-contain transition-transform duration-200 ease-out rounded-lg select-none"
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${zoom}) rotate(${rotation}deg)`,
              cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
            }}
            draggable={false}
            onMouseDown={handleMouseDown}
          />
        </div>

        {/* 底部信息栏 */}
        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/80 to-transparent rounded-b-xl">
          <div className="text-center">
            <p className="text-white/80 text-sm">
              {t("preview.shortcut")}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
