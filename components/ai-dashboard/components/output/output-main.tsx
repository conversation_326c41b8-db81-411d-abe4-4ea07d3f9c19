"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquare, Image, Video } from "lucide-react";
import { useTranslations } from "next-intl";
import { ResultDisplay } from "./ResultDisplay";
import type { WorkspaceGenerationResult } from "../types";

interface OutputMainProps {
  activeTab: string;
  isFullscreen: boolean;
  isMobile: boolean;
}

export function OutputMain({
  activeTab,
  isFullscreen,
  isMobile
}: OutputMainProps) {
  const t = useTranslations("ai-dashboard.results");

  return (
    <Card
      className={`${
        isFullscreen
          ? 'h-full max-h-full overflow-hidden '  // 全屏模式：严格限制高度，防止溢出
          : isMobile
            ? 'min-h-[40vh]'  // 优化：移动端减少最小高度
            : 'h-full'
      } flex flex-col gap-2 bg-gradient-to-br from-card via-card to-accent/5 w-full max-w-full`}
      style={isFullscreen ? { height: '100%', maxHeight: '100%' } : undefined}
    >
      <CardHeader className={`border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10 flex-shrink-0 ${
        isMobile ? 'px-2 py-1' : 'px-6 py-2'  // 优化：移动端最小header内边距
      }`}>
        <CardTitle className={`flex items-center  ${isMobile ? 'gap-1' : 'gap-2'}`}>  {/* 优化：移动端减少间距 */}
          <div className={`rounded-lg ${
            isMobile ? 'p-1.5' : 'p-2'  // 优化：移动端减少图标容器大小
          } ${
            activeTab === 'text' ? 'bg-gradient-to-r from-blue-500 to-purple-500' :
            activeTab === 'image' ? 'bg-gradient-to-r from-green-500 to-teal-500' :
            'bg-gradient-to-r from-orange-500 to-red-500'
          }`}>
            {activeTab === 'text' && <MessageSquare className={`text-white ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />}  {/* 优化：移动端减少图标大小 */}
            {activeTab === 'image' && <Image className={`text-white ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />}
            {activeTab === 'video' && <Video className={`text-white ${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />}
          </div>
          <span className={`bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${
            isMobile ? 'text-sm font-medium' : 'text-base font-semibold'  // 优化：移动端减少字体大小
          }`}>
            {isMobile ? t("result_ready") : t("result_ready").toUpperCase()}  {/* 优化：移动端不使用大写 */}
          </span>
        </CardTitle>
       
      </CardHeader>
      <CardContent
        className={`flex-1 min-h-0 ${
          isMobile ? 'p-1' : 'p-2'  // 优化：移动端最小内容区内边距
        } ${
          isFullscreen
            ? 'overflow-y-auto overflow-x-hidden'  // 全屏模式：强制内部滚动
            : 'overflow-y-auto'
        }`}
        style={isFullscreen ? {
          height: isMobile ? 'calc(100% - 50px)' : 'calc(100% - 120px)',  // 优化：移动端减去更小的header高度
          maxHeight: isMobile ? 'calc(100% - 50px)' : 'calc(100% - 120px)',
          overflowY: 'auto',
          overflowX: 'hidden'
        } : undefined}>
        <ResultDisplay />
      </CardContent>
    </Card>
  );
}
