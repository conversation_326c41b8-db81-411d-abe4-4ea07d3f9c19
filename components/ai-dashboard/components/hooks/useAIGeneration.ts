"use client";

import React, { useReducer, useEffect, useContext, createContext, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { useTranslations, useLocale } from "next-intl";
import { useModalStore } from "@/stores/modalStore";
import { getLocalActiveAIModels } from "@/services/provider/model-manager";
import { getAiModelTranslation } from "@/services/page";
import type { AIFormData, EnhancedGenerationResult, GenerationOptions } from "../types";
import { aiFormSchema } from "../types";
import { aiGenerationReducer, initialState, type AIGenerationAction, type AIGenerationState } from "../ai-generation.reducer";

// Context 定义
interface AIGenerationContextType {
  // 状态
  state: AIGenerationState;
  dispatch: React.Dispatch<AIGenerationAction>;
  
  // React Hook Form 实例
  form: ReturnType<typeof useForm<AIFormData>>;
  
  // 方法
  handleGenerate: () => Promise<void>;
  handleModelSelect: (modelId: string) => void;
  fetchUserCredits: () => Promise<void>;
  loadModels: () => Promise<void>;
}

const AIGenerationContext = createContext<AIGenerationContextType | null>(null);

// Provider 组件
export function AIGenerationProvider({ 
  children, 
  modelType,
  optionsConfigRef
}: { 
  children: React.ReactNode;
  modelType?: string;
  optionsConfigRef?: React.RefObject<{ uploadPendingImages: () => Promise<GenerationOptions>; hasPendingImages: () => boolean }>;
}) {
  const t = useTranslations("ai-dashboard");
  const locale = useLocale();
  const { openModal } = useModalStore();
  
  // Reducer 状态管理
  const [state, dispatch] = useReducer(aiGenerationReducer, {
    ...initialState,
    formData: {
      ...initialState.formData,
      modelType: (modelType as any) || 'text'
    }
  });
  
  // React Hook Form 设置
  const form = useForm<AIFormData>({
    resolver: zodResolver(aiFormSchema),
    defaultValues: state.formData
  });
  
  // 监听表单变化，同步到 reducer
  useEffect(() => {
    const subscription = form.watch((value) => {
      dispatch({ type: 'SET_FORM_DATA', payload: value as Partial<AIFormData> });
    });
    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // 初始化
  useEffect(() => {
    fetchUserCredits();
    loadModels();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locale]);
  
  // 模型类型变化时重新筛选
  useEffect(() => {
    if (modelType && state.selectedModel?.model_type !== modelType) {
      const filteredModels = state.models.filter(m => m.model_type === modelType);
      if (filteredModels.length > 0 && !filteredModels.find(m => m.model_id === state.selectedModel?.model_id)) {
        const newModel = filteredModels[0];
        dispatch({ type: 'SET_SELECTED_MODEL', payload: newModel });
        form.setValue('modelId', newModel.model_id);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modelType, state.models, state.selectedModel]);
  
  // 成本估算
  useEffect(() => {
    if (state.selectedModel && state.formData.prompt) {
      estimateCost();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.selectedModel, state.formData.prompt, state.formData.options]);


  
  // 业务逻辑方法
  const fetchUserCredits = useCallback(async () => {
    try {
      const response = await fetch('/api/get-user-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json() as any;
      if (data.code === 0) {
        dispatch({ type: 'SET_USER_CREDITS', payload: data.data.credits?.left_credits || 0 });
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  }, []);
  
  const loadModels = useCallback(async () => {
    try {
      dispatch({ type: 'SET_MODELS', payload: { models: [], loading: true, error: null } });
      
      const localModels = getLocalActiveAIModels();
      const translations = await getAiModelTranslation(locale);
      
      // 应用翻译
      const translatedModels = localModels.map(model => {
        if ((model.provider === 'grsai' || model.provider === 'replicate') && model.translationKey) {
          const [provider, ...modelIdParts] = model.translationKey.split('.');
          const normalizedModelId = modelIdParts.join('.').replace(/\./g, '-');
          const modelTranslation = translations.models[provider]?.[normalizedModelId];
          
          if (modelTranslation) {
            return {
              ...model,
              model_name: modelTranslation.name || model.model_name,
              description: modelTranslation.description || model.description
            };
          }
        }
        return model;
      });
      
      dispatch({ type: 'SET_MODELS', payload: { models: translatedModels as any, loading: false, error: null } });
      
      // 自动选择第一个模型
      const filteredModels = modelType 
        ? translatedModels.filter(m => m.model_type === modelType)
        : translatedModels;
      
      if (filteredModels.length > 0 && !state.selectedModel) {
        const firstModel = filteredModels[0];
        dispatch({ type: 'SET_SELECTED_MODEL', payload: firstModel as any });
        form.setValue('modelId', firstModel.model_id);
      }
    } catch (error) {
      dispatch({ type: 'SET_MODELS', payload: { models: [], loading: false, error: 'Failed to load models' } });
    }
  }, [locale, modelType, state.selectedModel, form]);
  
  const handleModelSelect = useCallback((modelId: string) => {
    const model = state.models.find(m => m.model_id === modelId);
    if (model) {
      dispatch({ type: 'SET_SELECTED_MODEL', payload: model });
      form.setValue('modelId', modelId);
    }
  }, [state.models, form]);
  
  const estimateCost = useCallback(async () => {
    if (!state.selectedModel || !state.formData.prompt) return;
    
    try {
      const response = await fetch('/api/ai/estimate-cost', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: state.selectedModel.model_id,
          type: state.selectedModel.model_type,
          prompt: state.formData.prompt,
          options: state.formData.options
        })
      });
      const data = await response.json() as any;
      if (data.code === 0) {
        dispatch({ type: 'SET_COST_ESTIMATE', payload: data.data });
      }
    } catch (error) {
      console.error('Failed to estimate cost:', error);
    }
  }, [state.selectedModel, state.formData]);
  
  const handleGenerate = useCallback(async () => {
    // 表单验证
    const isValid = await form.trigger();
    if (!isValid) {
      toast.error(t("errors.invalid_input"));
      return;
    }
    
    if (!state.selectedModel || !state.formData.prompt.trim()) {
      toast.error(t("errors.invalid_input"));
      return;
    }

    if (state.costEstimate && !state.costEstimate.user_credits.can_afford) {
      toast.error(t("errors.insufficient_credits"));
      return;
    }

    dispatch({ type: 'SET_LOADING', payload: true });
    
    // 不要清空结果，而是设置一个初始的"生成中"状态
    const initialResult = {
      id: Date.now().toString(),
      task_id: '',
      type: state.selectedModel?.model_type || state.formData.modelType,
      status: 'pending',
      progress: 0
    };
    dispatch({ type: 'SET_GENERATION_RESULT', payload: initialResult });

    try {
      // 在提交前先上传所有待上传的图片
      let finalOptions = state.formData.options || {};
      console.log('[AI Generation] Checking for pending images...');
      console.log('[AI Generation] optionsConfigRef.current:', !!optionsConfigRef?.current);

      if (optionsConfigRef?.current) {
        const hasPending = optionsConfigRef.current.hasPendingImages();
        console.log('[AI Generation] hasPendingImages result:', hasPending);

        if (hasPending) {
          toast.info("正在上传图片...");
          try {
            console.log('[AI Generation] Calling uploadPendingImages...');
            const uploadedOptions = await optionsConfigRef.current.uploadPendingImages();
            finalOptions = uploadedOptions as any;
            console.log('[AI Generation] Images uploaded, updated options:', {
              ...finalOptions,
              uploadedImages: finalOptions.uploadedImages?.map((url: string) =>
                url.startsWith('data:') ? url.substring(0, 50) + '...' : url
              )
            });
          } catch (uploadError) {
            console.error('[AI Generation] Image upload failed:', uploadError);
            toast.error("图片上传失败，请重试");
            dispatch({ type: 'SET_LOADING', payload: false });
            return;
          }
        }
      }

      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          model: state.formData.modelId,
          type: state.selectedModel?.model_type || state.formData.modelType, // 使用模型的实际类型
          prompt: state.formData.prompt,
          options: finalOptions
        })
      });

      const data = await response.json() as any;

      if (data.code === 0) {
        const result = data.data;
        dispatch({ type: 'SET_GENERATION_RESULT', payload: result });
        
        if (result.status === 'success') {
          // 成功时才设置 loading: false
          dispatch({ type: 'SET_LOADING', payload: false });
          // 添加到历史记录
          const enhancedResult: EnhancedGenerationResult = {
            ...result,
            formData: state.formData,
            selectedModel: state.selectedModel,
            timestamp: new Date(),
            creditsUsed: result.usage?.credits_consumed
          };
          dispatch({ type: 'ADD_TO_HISTORY', payload: enhancedResult });
          toast.success(t("status.success"));
          fetchUserCredits();
        } else if (result.status === 'pending' || result.status === 'running') {
          // 开始轮询，但保持 loading: true
          pollResult(result.task_id);
        }
      } else if (data.code === -2) {
        // 错误时设置 loading: false
        dispatch({ type: 'SET_LOADING', payload: false });
        openModal('login', {});
      } else {
        // 错误时设置 loading: false
        dispatch({ type: 'SET_LOADING', payload: false });
        toast.error(data.msg || t("errors.generation_failed"));
      }
    } catch (error) {
      // 错误时设置 loading: false
      dispatch({ type: 'SET_LOADING', payload: false });
      toast.error(t("errors.network_error"));
    }
    // 移除 finally 块中的 loading: false
  }, [form, state, t, openModal, fetchUserCredits, optionsConfigRef]);
  
  const pollResult = useCallback(async (taskId: string) => {
    const maxAttempts = 60;
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch('/api/ai/result', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ task_id: taskId })
        });

        const data = await response.json() as any;

        if (data.code === 0) {
          dispatch({ type: 'SET_GENERATION_RESULT', payload: data.data });

          if (data.data.status === 'success') {
            // 成功时才设置 loading: false
            dispatch({ type: 'SET_LOADING', payload: false });
            const enhancedResult: EnhancedGenerationResult = {
              ...data.data,
              formData: state.formData,
              selectedModel: state.selectedModel,
              timestamp: new Date(),
              creditsUsed: data.data.usage?.credits_consumed
            };
            dispatch({ type: 'ADD_TO_HISTORY', payload: enhancedResult });
            toast.success(t("status.success"));
            fetchUserCredits();
            return;
          } else if (data.data.status === 'failed') {
            // 失败时设置 loading: false
            dispatch({ type: 'SET_LOADING', payload: false });
            toast.error(t("errors.generation_failed", { detail: data.data.error?.detail || "Unknown error" }));
            return;
          }
        }

        // 继续轮询，保持 loading: true
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          // 超时时设置 loading: false
          dispatch({ type: 'SET_LOADING', payload: false });
          toast.error(t("errors.network_error"));
        }
      } catch (error) {
        console.error('Polling error:', error);
        // 错误时的处理也要设置 loading: false
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          dispatch({ type: 'SET_LOADING', payload: false });
        }
      }
    };

    poll();
  }, [state, t, fetchUserCredits]);
  
  const value = {
    state,
    dispatch,
    form,
    handleGenerate,
    handleModelSelect,
    fetchUserCredits,
    loadModels
  };
  
  return React.createElement(
    AIGenerationContext.Provider,
    { value },
    children
  );
}

// Hook
export function useAIGeneration() {
  const context = useContext(AIGenerationContext);
  if (!context) {
    throw new Error('useAIGeneration must be used within AIGenerationProvider');
  }
  return context;
}