import type { AIM<PERSON>l, GenerationR<PERSON>ult, CostEstimate, AIFormData, EnhancedGenerationResult } from "./types";

export interface AIGenerationState {
  // 表单数据
  formData: AIFormData;
  
  // 模型相关
  selectedModel: AIModel | null;
  models: AIModel[];
  modelsLoading: boolean;
  modelsError: string | null;
  
  // 生成状态
  loading: boolean;
  generationResult: GenerationResult | null;
  generationHistory: EnhancedGenerationResult[];
  
  // 成本和积分
  costEstimate: CostEstimate | null;
  userCredits: number;
}

export type AIGenerationAction = 
  | { type: 'SET_FORM_DATA'; payload: Partial<AIFormData> }
  | { type: 'SET_SELECTED_MODEL'; payload: AIModel | null }
  | { type: 'SET_MODELS'; payload: { models: AIModel[]; loading: boolean; error: string | null } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_GENERATION_RESULT'; payload: GenerationResult | null }
  | { type: 'ADD_TO_HISTORY'; payload: EnhancedGenerationResult }
  | { type: 'SET_COST_ESTIMATE'; payload: CostEstimate | null }
  | { type: 'SET_USER_CREDITS'; payload: number };

export const initialState: AIGenerationState = {
  formData: {
    modelType: 'text',
    modelId: '',
    prompt: '',
    options: {
      size: '1:1',
      aspectRatio: '1:1',
      variants: 1,
      temperature: 0.7,
      max_tokens: 1000,
      cdn: 'global',
      uploadedImages: [],
      referenceImages: []
    }
  },
  selectedModel: null,
  models: [],
  modelsLoading: false,
  modelsError: null,
  loading: false,
  generationResult: null,
  generationHistory: [],
  costEstimate: null,
  userCredits: 0
};

export function aiGenerationReducer(
  state: AIGenerationState, 
  action: AIGenerationAction
): AIGenerationState {
  switch (action.type) {
    case 'SET_FORM_DATA':
      return {
        ...state,
        formData: { ...state.formData, ...action.payload }
      };
    
    case 'SET_SELECTED_MODEL':
      return {
        ...state,
        selectedModel: action.payload,
        formData: {
          ...state.formData,
          modelId: action.payload?.model_id || ''
        }
      };
    
    case 'SET_MODELS':
      return {
        ...state,
        models: action.payload.models,
        modelsLoading: action.payload.loading,
        modelsError: action.payload.error
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    
    case 'SET_GENERATION_RESULT':
      return {
        ...state,
        generationResult: action.payload
      };
    
    case 'ADD_TO_HISTORY':
      return {
        ...state,
        generationHistory: [action.payload, ...state.generationHistory.slice(0, 9)] // 保留最近10条
      };
    
    case 'SET_COST_ESTIMATE':
      return {
        ...state,
        costEstimate: action.payload
      };
    
    case 'SET_USER_CREDITS':
      return {
        ...state,
        userCredits: action.payload
      };
    
    default:
      return state;
  }
}