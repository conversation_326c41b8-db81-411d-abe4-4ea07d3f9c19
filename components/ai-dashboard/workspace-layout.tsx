"use client";

import { InputMain, AIFormContent } from "./input-main";
import { useDeviceLayout } from "./hooks/use-device-layout";
import { useResponsiveStyles } from "./hooks/use-responsive-styles";
import {
  WorkspaceToolbar,
  ModelTypeTabs,
  OutputMain,
  useWorkspaceState,
  useWorkspaceFullscreen
} from "./components";
import { useAppContext } from "@/contexts/app";

import { AIGenerationProvider } from "./components/hooks/useAIGeneration";
import { useRef } from "react";
import { DynamicOptionsConfigRef } from "./components/input/DynamicOptionsConfig";

export function WorkspaceLayout() {
  const { user } = useAppContext();

  // // 未登录不显示这个组件
  // if (!user) {
  //   return null;
  // }

  // 使用自定义hooks管理状态
  const {
    activeTab,
    handleTabChange
  } = useWorkspaceState();

  const {
    isFullscreen,
    moduleRef,
    handleFullscreenToggle
  } = useWorkspaceFullscreen();

  const { isDesktop, isMobile } = useDeviceLayout();
  
  // 创建 DynamicOptionsConfig 的引用，提升到顶层
  const optionsConfigRef = useRef<DynamicOptionsConfigRef>(null);

  // 在组件顶层调用所有hooks，避免条件调用
  const { className: typeButtonClassName } = useResponsiveStyles({
    mobile: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-sm font-medium',
      width: 'w-full justify-start',
    },
    tablet: {
      size: 'h-12 px-4',
      direction: 'flex-row',
      gap: 'gap-3',
      text: 'text-base font-medium',
      width: 'w-full justify-start',
    },
    desktop: {
      size: 'h-20 p-3',
      direction: 'flex-col',
      gap: 'gap-2',
      text: 'text-xs font-medium',
      width: 'w-full justify-center',
    },
  });

  return (
    <AIGenerationProvider 
      modelType={activeTab}
      optionsConfigRef={optionsConfigRef}
    >
      <div
        ref={moduleRef}
        className={`
          ${isFullscreen
            ? "fixed inset-0 flex flex-col"
            : "container mx-auto max-w-6xl"
          }
          transition-all duration-300 ease-in-out
        `}
        style={isFullscreen ? {
          zIndex: 40,
          background: 'var(--background)',
          height: '100vh',
          maxHeight: '100vh',
          overflow: 'hidden'
        } : undefined}
      >
        {/* 统一的工具栏组件 - 自动根据全屏状态切换显示模式 */}
        <WorkspaceToolbar
          isFullscreen={isFullscreen}
          onToggleFullscreen={handleFullscreenToggle}
        />

        {/* 主要内容区域 - 使用响应式容器 */}
        <div className={isFullscreen ?
          isMobile
            ? "px-0 pb-0 pt-0 flex-1 min-h-0 overflow-hidden"  // 优化：移动端全屏完全无内边距，直接占满宽度
            : "px-6 pb-6 pt-4 flex-1 min-h-0 overflow-hidden"
          : isMobile
            ? "px-1 pb-6"  // 优化：移动端常规模式减少内边距
            : "px-2 md:px-6 pb-12"
        }>
          <div className={
            isFullscreen
              ? isMobile
                ? "h-full flex flex-col gap-1 min-h-0"  // 优化：移动端全屏最小间距
                : "h-full flex gap-6 min-h-0"  // 桌面端全屏：flex布局，自动填充空间
              : isMobile
                ? "flex flex-col gap-3"  // 优化：移动端常规模式减少间距
                : "grid grid-cols-5 gap-6"
          } style={!isFullscreen && !isMobile ? { alignItems: 'stretch', gridAutoRows: '1fr' } : {}}>
            
            {/* 左栏：模型类型选择器 - 只在桌面端全屏时显示 */}
            {(isFullscreen && isDesktop && !isMobile) && (
              <div className="flex-[1] h-full p-4 bg-gradient-to-b from-muted/30 to-muted/10 rounded-2xl border border-border/30">
                <h3 className="text-sm font-semibold text-foreground mb-4 flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-primary to-accent"></div>
                  MODEL TYPE
                </h3>
                <ModelTypeTabs
                  activeTab={activeTab}
                  onTabChange={handleTabChange}
                  variant="buttons"
                  isFullscreen={isFullscreen}
                  typeButtonClassName={typeButtonClassName}
                />
              </div>
            )}

            {/* 中栏：模型选择和参数设置 */}
            <div className={`${
              isFullscreen
                ? isMobile
                  ? 'w-full h-full min-h-0'  // 修复：移动端全屏添加h-full，确保占满高度
                  : 'flex-[2] h-full min-h-0'  // 桌面端全屏：flex比例2，限制高度
                : isMobile
                  ? 'w-full'
                  : 'col-span-2'
            } flex flex-col ${isFullscreen ? 'overflow-hidden' : ''}`}>  {/* 修复：简化overflow设置，避免过度限制 */}
              {/* 移动端和非全屏桌面端显示水平标签页 */}
              {!(isFullscreen && isDesktop && !isMobile) && (
                <ModelTypeTabs
                  activeTab={activeTab}
                  onTabChange={handleTabChange}
                />
              )}

              {/* AI生成器组件 - 移除Provider，因为已经在顶层包裹了 */}
              <div className={isFullscreen ? 'flex-1 min-h-0' : ''}>
                <AIFormContent 
                  isFullscreen={isFullscreen}
                  optionsConfigRef={optionsConfigRef}
                />
              </div>
            </div>

            {/* 右栏：结果展示 */}
            <div className={`${
              isFullscreen
                ? isMobile
                  ? 'w-full flex-1 min-h-0 overflow-hidden'  // 移动端全屏：占满剩余空间，严格限制高度
                  : 'flex-[3] h-full min-h-0 overflow-hidden'  // 桌面端全屏：flex比例3，严格限制高度
                : isMobile
                  ? 'w-full mt-6'
                  : 'col-span-3 h-full'
            }`}>
              <OutputMain
                activeTab={activeTab}
                isFullscreen={isFullscreen}
                isMobile={isMobile}
              />
            </div>
          </div>
        </div>
      </div>
    </AIGenerationProvider>
  );
}
