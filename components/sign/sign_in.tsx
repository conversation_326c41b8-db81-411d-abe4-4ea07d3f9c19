"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useModalStore } from "@/stores/modalStore";
import { useTranslations } from "next-intl";

export default function SignIn() {
  const t = useTranslations();
  const { openModal } = useModalStore();

  return (
    <Button
      variant="default"
      onClick={() => openModal('login', {})}
      className="cursor-pointer"
    >
      {t("user.sign_in")}
    </Button>
  );
}
