"use client";

import { BsMoonStars, BsSun } from "react-icons/bs";

import { <PERSON>ache<PERSON>ey } from "@/services/constant";
import { cacheSet } from "@/lib/cache";
import { useAppContext } from "@/contexts/app";

export default function () {
  const { theme, setTheme } = useAppContext();

  const handleThemeChange = function (_theme: string) {
    if (_theme === theme) {
      return;
    }

    cacheSet(CacheKey.Theme, _theme, -1);
    setTheme(_theme);
  };

  return (
    <div className="flex items-center justify-center h-10 w-10 rounded-md hover:bg-accent/10 transition-colors">
      {theme === "dark" ? (
        <BsSun
          className="cursor-pointer text-xl text-muted-foreground hover:text-foreground transition-colors"
          onClick={() => handleThemeChange("light")}
        />
      ) : (
        <BsMoonStars
          className="cursor-pointer text-xl text-muted-foreground hover:text-foreground transition-colors"
          onClick={() => handleThemeChange("dark")}
        />
      )}
    </div>
  );
}
