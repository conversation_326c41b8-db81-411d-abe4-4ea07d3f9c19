"use client"

import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { CheckIcon, ChevronDownIcon, ChevronUpIcon } from "lucide-react"
import { cn } from "@/lib/utils"

// 扩展的选项接口，支持图标和描述
export interface RichSelectOption {
  value: string
  label: string
  description?: string
  icon?: React.ReactNode | string // 支持React组件或CSS类名
  badge?: string // 支持在标题旁边显示徽章（如价格）
  disabled?: boolean
}

// RichSelect根组件
function RichSelect({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Root>) {
  return <SelectPrimitive.Root data-slot="rich-select" {...props} />
}

// RichSelectGroup组件
function RichSelectGroup({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Group>) {
  return <SelectPrimitive.Group data-slot="rich-select-group" {...props} />
}

// RichSelectValue组件
function RichSelectValue({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Value>) {
  return <SelectPrimitive.Value data-slot="rich-select-value" {...props} />
}

// RichSelectTrigger组件 - 支持更高的高度
function RichSelectTrigger({
  className,
  size = "default",
  children,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {
  size?: "sm" | "default" | "lg"
}) {
  return (
    <SelectPrimitive.Trigger
      data-slot="rich-select-trigger"
      data-size={size}
      className={cn(
        "border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        // 不同尺寸的高度
        "data-[size=sm]:h-8 data-[size=default]:h-9 data-[size=lg]:h-14",
        // 选中值的样式
        "*:data-[slot=rich-select-value]:line-clamp-1 *:data-[slot=rich-select-value]:flex *:data-[slot=rich-select-value]:items-center *:data-[slot=rich-select-value]:gap-2",
        "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      )}
      {...props}
    >
      {children}
      <SelectPrimitive.Icon asChild>
        <ChevronDownIcon className="size-4 opacity-50" />
      </SelectPrimitive.Icon>
    </SelectPrimitive.Trigger>
  )
}

// RichSelectContent组件
function RichSelectContent({
  className,
  children,
  position = "popper",
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Content>) {
  return (
    <SelectPrimitive.Portal>
      <SelectPrimitive.Content
        data-slot="rich-select-content"
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",
          position === "popper" &&
            "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
          className
        )}
        position={position}
        {...props}
      >
        <RichSelectScrollUpButton />
        <SelectPrimitive.Viewport
          className={cn(
            "p-1",
            position === "popper" &&
              "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"
          )}
        >
          {children}
        </SelectPrimitive.Viewport>
        <RichSelectScrollDownButton />
      </SelectPrimitive.Content>
    </SelectPrimitive.Portal>
  )
}

// RichSelectLabel组件
function RichSelectLabel({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Label>) {
  return (
    <SelectPrimitive.Label
      data-slot="rich-select-label"
      className={cn("text-muted-foreground px-2 py-1.5 text-xs", className)}
      {...props}
    />
  )
}

// 渲染图标的辅助函数
function renderIcon(icon: React.ReactNode | string | undefined, className?: string) {
  if (!icon) return null
  
  // 如果是字符串，假设是CSS类名（如UnoCSS图标）
  if (typeof icon === 'string') {
    return <span className={cn(icon, className)} />
  }
  
  // 如果是React组件
  return <span className={className}>{icon}</span>
}

// RichSelectItem组件 - 支持图标和描述
function RichSelectItem({
  className,
  children,
  option,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Item> & {
  option?: RichSelectOption
}) {
  return (
    <SelectPrimitive.Item
      data-slot="rich-select-item"
      className={cn(
        "focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-3 rounded-sm py-2 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
        // 如果有描述，增加垂直间距
        option?.description && "py-3",
        className
      )}
      {...props}
    >
      {/* 图标 */}
      {option?.icon && renderIcon(option.icon, "size-8 shrink-0")}
      
      {/* 文本内容 */}
      <div className="flex flex-col gap-0.5 flex-1 min-w-0">
        <SelectPrimitive.ItemText asChild>
          <div className="flex items-center gap-2">
            <span className="text-sm font-semibold truncate">
              {option?.label || children}
            </span>
            {option?.badge && (
              <span className="inline-flex items-center rounded-full px-2 py-0 text-xs font-medium bg-gradient-to-r from-orange-200 to-amber-200 text-amber-700   shrink-0">
                {option.badge}
              </span>
            )}
          </div>
        </SelectPrimitive.ItemText>
        {option?.description && (
          <span className="text-xs font-medium text-muted-foreground line-clamp-2">
            {option.description}
          </span>
        )}
      </div>
      
      {/* 选中指示器 */}
      <span className="absolute right-2 flex size-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <CheckIcon className="size-4" />
        </SelectPrimitive.ItemIndicator>
      </span>
    </SelectPrimitive.Item>
  )
}

// RichSelectSeparator组件
function RichSelectSeparator({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Separator>) {
  return (
    <SelectPrimitive.Separator
      data-slot="rich-select-separator"
      className={cn("bg-border pointer-events-none -mx-1 my-1 h-px", className)}
      {...props}
    />
  )
}

// 滚动按钮组件
function RichSelectScrollUpButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {
  return (
    <SelectPrimitive.ScrollUpButton
      data-slot="rich-select-scroll-up-button"
      className={cn(
        "flex cursor-default items-center justify-center py-1",
        className
      )}
      {...props}
    >
      <ChevronUpIcon className="size-4" />
    </SelectPrimitive.ScrollUpButton>
  )
}

function RichSelectScrollDownButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {
  return (
    <SelectPrimitive.ScrollDownButton
      data-slot="rich-select-scroll-down-button"
      className={cn(
        "flex cursor-default items-center justify-center py-1",
        className
      )}
      {...props}
    >
      <ChevronDownIcon className="size-4" />
    </SelectPrimitive.ScrollDownButton>
  )
}

export {
  RichSelect,
  RichSelectContent,
  RichSelectGroup,
  RichSelectItem,
  RichSelectLabel,
  RichSelectScrollDownButton,
  RichSelectScrollUpButton,
  RichSelectSeparator,
  RichSelectTrigger,
  RichSelectValue,
}
