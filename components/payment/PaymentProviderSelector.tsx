'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface PaymentProvider {
  id: string;
  name: string;
  description: string;
  logo?: string;
  enabled: boolean;
}

interface PaymentProviderSelectorProps {
  providers: PaymentProvider[];
  selectedProvider: string;
  onProviderChange: (provider: string) => void;
}

export default function PaymentProviderSelector({
  providers,
  selectedProvider,
  onProviderChange,
}: PaymentProviderSelectorProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">选择支付方式</h3>
      <RadioGroup value={selectedProvider} onValueChange={onProviderChange}>
        {providers.filter(p => p.enabled).map((provider) => (
          <div key={provider.id} className="flex items-center space-x-2">
            <RadioGroupItem value={provider.id} id={provider.id} />
            <Label htmlFor={provider.id} className="flex-1">
              <Card className="cursor-pointer hover:bg-gray-50">
                <CardContent className="flex items-center p-4">
                  {provider.logo && (
                    <img
                      src={provider.logo}
                      alt={provider.name}
                      className="w-8 h-8 mr-3"
                    />
                  )}
                  <div>
                    <div className="font-medium">{provider.name}</div>
                    <div className="text-sm text-gray-500">{provider.description}</div>
                  </div>
                </CardContent>
              </Card>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}
