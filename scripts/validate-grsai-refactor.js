#!/usr/bin/env node

/**
 * 验证GRSAI重构的简单脚本
 * 检查模型配置和代码的一致性
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 验证GRSAI统一API重构...\n');

// 验证1: 检查API客户端是否有统一方法
function validateAPIClient() {
  console.log('1️⃣ 检查API客户端统一方法...');
  
  const apiFilePath = 'services/provider/grsai/api.ts';
  const apiContent = fs.readFileSync(apiFilePath, 'utf8');
  
  // 检查是否有makeGenerationRequest方法
  if (apiContent.includes('makeGenerationRequest')) {
    console.log('   ✅ 找到统一方法 makeGenerationRequest');
  } else {
    console.log('   ❌ 未找到统一方法 makeGenerationRequest');
    return false;
  }
  
  // 检查是否有弃用标记
  if (apiContent.includes('@deprecated')) {
    console.log('   ✅ 旧方法已标记为弃用');
  } else {
    console.log('   ⚠️  旧方法未标记为弃用');
  }
  
  return true;
}

// 验证2: 检查Service层是否使用模型配置
function validateServiceLayer() {
  console.log('\n2️⃣ 检查Service层使用模型配置...');
  
  const serviceFilePath = 'services/provider/grsai/service.ts';
  const serviceContent = fs.readFileSync(serviceFilePath, 'utf8');
  
  // 检查是否使用model.api_endpoint
  if (serviceContent.includes('model.api_endpoint')) {
    console.log('   ✅ Service层使用模型配置的api_endpoint');
  } else {
    console.log('   ❌ Service层未使用模型配置的api_endpoint');
    return false;
  }
  
  // 检查是否调用统一方法
  if (serviceContent.includes('makeGenerationRequest')) {
    console.log('   ✅ Service层调用统一方法');
  } else {
    console.log('   ❌ Service层未调用统一方法');
    return false;
  }
  
  return true;
}

// 验证3: 检查类型定义
function validateTypeDefinitions() {
  console.log('\n3️⃣ 检查类型定义...');
  
  const typesFilePath = 'services/provider/grsai/api-types.ts';
  const typesContent = fs.readFileSync(typesFilePath, 'utf8');
  
  // 检查是否有统一的请求类型
  if (typesContent.includes('GRSAIUnifiedRequest')) {
    console.log('   ✅ 找到统一请求类型 GRSAIUnifiedRequest');
  } else {
    console.log('   ❌ 未找到统一请求类型');
    return false;
  }
  
  // 检查是否有统一的响应类型
  if (typesContent.includes('GRSAIUnifiedResponse')) {
    console.log('   ✅ 找到统一响应类型 GRSAIUnifiedResponse');
  } else {
    console.log('   ❌ 未找到统一响应类型');
    return false;
  }
  
  return true;
}

// 验证4: 检查模型配置的一致性
function validateModelConfigurations() {
  console.log('\n4️⃣ 检查模型配置一致性...');
  
  const textModelsPath = 'services/provider/grsai/text-models.ts';
  const mediaModelsPath = 'services/provider/grsai/media-models.ts';
  
  let allValid = true;
  
  // 检查文本模型配置
  if (fs.existsSync(textModelsPath)) {
    const textContent = fs.readFileSync(textModelsPath, 'utf8');
    if (textContent.includes('api_endpoint:')) {
      console.log('   ✅ 文本模型配置包含api_endpoint');
    } else {
      console.log('   ❌ 文本模型配置缺少api_endpoint');
      allValid = false;
    }
  }
  
  // 检查媒体模型配置
  if (fs.existsSync(mediaModelsPath)) {
    const mediaContent = fs.readFileSync(mediaModelsPath, 'utf8');
    if (mediaContent.includes('api_endpoint:')) {
      console.log('   ✅ 媒体模型配置包含api_endpoint');
    } else {
      console.log('   ❌ 媒体模型配置缺少api_endpoint');
      allValid = false;
    }
  }
  
  return allValid;
}

// 验证5: 检查重构前后的关键差异
function validateRefactoringChanges() {
  console.log('\n5️⃣ 检查重构关键变化...');
  
  const serviceContent = fs.readFileSync('services/provider/grsai/service.ts', 'utf8');
  
  // 检查是否移除了硬编码的endpoint选择
  const hasHardcodedEndpoints = serviceContent.includes("endpoint = '/v1/") && 
                               !serviceContent.includes('makeGenerationRequest');
  
  if (!hasHardcodedEndpoints) {
    console.log('   ✅ 已移除硬编码的endpoint选择');
  } else {
    console.log('   ❌ 仍存在硬编码的endpoint选择');
    return false;
  }
  
  // 检查是否使用了配置驱动的方式
  if (serviceContent.includes('model.api_endpoint')) {
    console.log('   ✅ 使用配置驱动的endpoint选择');
  } else {
    console.log('   ❌ 未使用配置驱动的endpoint选择');
    return false;
  }
  
  return true;
}

// 主验证函数
async function main() {
  let allTestsPassed = true;
  
  try {
    // 运行所有验证
    const results = [
      validateAPIClient(),
      validateServiceLayer(), 
      validateTypeDefinitions(),
      validateModelConfigurations(),
      validateRefactoringChanges()
    ];
    
    allTestsPassed = results.every(result => result);
    
    // 输出总结
    console.log('\n' + '='.repeat(50));
    console.log('📊 验证结果总结:');
    
    if (allTestsPassed) {
      console.log('🎉 所有验证通过！重构成功完成！');
      console.log('\n✨ 重构带来的改进:');
      console.log('  ✅ 消除了endpoint重复定义');
      console.log('  ✅ 实现了统一的API接口');
      console.log('  ✅ 基于模型配置的动态路由');
      console.log('  ✅ 更易于维护和扩展');
      
      console.log('\n🚀 现在可以测试实际功能:');
      console.log('  1. 启动开发服务器: npm run dev');
      console.log('  2. 测试文本生成: 选择 gemini-2.5-flash-lite');
      console.log('  3. 检查日志确认使用了正确的endpoint');
      console.log('  4. 验证不同类型模型都能正常工作');
      
    } else {
      console.log('❌ 部分验证失败，重构可能不完整');
      console.log('请检查上述失败项并修复');
    }
    
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error.message);
    allTestsPassed = false;
  }
  
  process.exit(allTestsPassed ? 0 : 1);
}

main().catch(console.error);