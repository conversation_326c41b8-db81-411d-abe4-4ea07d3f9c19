#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// 命令行参数
const args = process.argv.slice(2);
const options = {
  fix: args.includes('--fix'),
  report: args.includes('--report'),
  csv: args.includes('--csv'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help') || args.includes('-h')
};

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 显示帮助信息
function showHelp() {
  console.log(colorize('🔍 环境变量检查工具', 'bold'));
  console.log();
  console.log(colorize('用法:', 'blue'));
  console.log('  pnpm checkenv [选项]');
  console.log();
  console.log(colorize('选项:', 'blue'));
  console.log('  --fix      自动修复缺失的变量（添加到对应文件）');
  console.log('  --report   生成详细的 JSON 报告文件');
  console.log('  --csv      生成 CSV 格式报告');
  console.log('  --verbose  显示详细输出');
  console.log('  --help, -h 显示此帮助信息');
  console.log();
  console.log(colorize('示例:', 'blue'));
  console.log('  pnpm checkenv              # 基本检查（表格格式）');
  console.log('  pnpm checkenv --fix        # 检查并自动修复');
  console.log('  pnpm checkenv --report     # 生成 JSON 报告文件');
  console.log('  pnpm checkenv --csv        # 生成 CSV 报告文件');
  console.log('  pnpm checkenv --fix --verbose  # 详细模式修复');
}

// 解析环境变量文件
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const variables = new Map();
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // 跳过注释和空行
    if (!line || line.startsWith('#')) {
      continue;
    }

    // 匹配环境变量格式: KEY = "value" 或 KEY = value
    const match = line.match(/^([A-Z_][A-Z0-9_]*)\s*=\s*(.*)$/);
    if (match) {
      const [, key, value] = match;
      variables.set(key, {
        value: value.trim(),
        line: i + 1,
        isEmpty: !value.trim() || value.trim() === '""' || value.trim() === "''"
      });
    }
  }

  return variables;
}

// 获取所有环境变量文件
function getEnvFiles() {
  const envFiles = [
    '.env.example',
    '.env.local',
    '.env.production',
    '.env.volcengine.example'
  ];

  const existingFiles = [];
  
  for (const file of envFiles) {
    const filePath = path.join(rootDir, file);
    if (fs.existsSync(filePath)) {
      existingFiles.push({
        name: file,
        path: filePath,
        variables: parseEnvFile(filePath)
      });
    }
  }

  return existingFiles;
}

// 自动修复缺失的变量
function fixMissingVariables(envFiles, baseVariables) {
  console.log(colorize('🔧 开始自动修复...', 'blue'));
  console.log();

  const otherFiles = envFiles.filter(f => f.name !== '.env.example');
  let totalFixed = 0;

  for (const file of otherFiles) {
    if (!file.variables) continue;

    const missing = [];
    baseVariables.forEach((baseVar, key) => {
      if (!file.variables.has(key)) {
        missing.push({ key, value: baseVar.value });
      }
    });

    if (missing.length > 0) {
      console.log(colorize(`📝 修复 ${file.name}...`, 'yellow'));
      
      try {
        // 读取原文件内容
        let content = fs.readFileSync(file.path, 'utf8');
        
        // 在文件末尾添加缺失的变量
        const additions = missing.map(({ key, value }) => `${key} = ${value}`).join('\n');
        content += '\n\n# 自动添加的缺失变量\n' + additions + '\n';
        
        // 写回文件
        fs.writeFileSync(file.path, content, 'utf8');
        
        console.log(colorize(`  ✅ 已添加 ${missing.length} 个变量`, 'green'));
        missing.forEach(({ key }) => {
          console.log(`    • ${key}`);
        });
        
        totalFixed += missing.length;
      } catch (error) {
        console.log(colorize(`  ❌ 修复失败: ${error.message}`, 'red'));
      }
      console.log();
    }
  }

  if (totalFixed > 0) {
    console.log(colorize(`🎉 修复完成! 总共添加了 ${totalFixed} 个变量`, 'green'));
  } else {
    console.log(colorize('✅ 没有需要修复的变量', 'green'));
  }
}

// 表格显示工具
function displayTable(envFiles, baseVariables) {
  const allVariables = new Set();
  
  // 收集所有变量名
  envFiles.forEach(file => {
    if (file.variables) {
      file.variables.forEach((_, key) => {
        allVariables.add(key);
      });
    }
  });

  const sortedVariables = Array.from(allVariables).sort();
  const fileNames = envFiles.map(f => f.name);
  
  console.log(colorize('📊 环境变量对比表格:', 'bold'));
  console.log();

  // 计算列宽
  const maxVarNameLength = Math.max(8, ...sortedVariables.map(v => v.length));
  const colWidth = Math.max(12, ...fileNames.map(f => f.length));
  
  // 表头
  const header = '变量名'.padEnd(maxVarNameLength) + ' │ ' + 
    fileNames.map(name => name.padEnd(colWidth)).join(' │ ');
  console.log(header);
  console.log('─'.repeat(header.length));

  // 表格内容
  for (const varName of sortedVariables) {
    let row = varName.padEnd(maxVarNameLength) + ' │ ';
    
    const cells = fileNames.map(fileName => {
      const file = envFiles.find(f => f.name === fileName);
      if (!file || !file.variables) return '❌'.padEnd(colWidth);
      
      const variable = file.variables.get(varName);
      if (!variable) {
        return colorize('❌ 缺失', 'red').padEnd(colWidth + 10); // +10 for color codes
      } else if (variable.isEmpty) {
        return colorize('⚠️  空值', 'yellow').padEnd(colWidth + 10);
      } else {
        return colorize('✅ 存在', 'green').padEnd(colWidth + 10);
      }
    });
    
    row += cells.join(' │ ');
    console.log(row);
  }
  
  console.log();
  
  // 统计信息
  console.log(colorize('📈 统计信息:', 'blue'));
  fileNames.forEach(fileName => {
    const file = envFiles.find(f => f.name === fileName);
    if (!file || !file.variables) return;
    
    let missing = 0, empty = 0, total = 0;
    
    for (const varName of sortedVariables) {
      const variable = file.variables.get(varName);
      if (!variable) {
        missing++;
      } else if (variable.isEmpty) {
        empty++;
      }
      total++;
    }
    
    const present = total - missing;
    const percentage = total > 0 ? ((present / total) * 100).toFixed(1) : '0.0';
    
    console.log(`  ${fileName}: ${present}/${total} 个变量 (${percentage}%)${missing > 0 ? colorize(` - ${missing} 个缺失`, 'red') : ''}${empty > 0 ? colorize(` - ${empty} 个空值`, 'yellow') : ''}`);
  });
  
  console.log();
}

// 生成CSV报告
function generateCSVReport(envFiles, baseVariables) {
  const allVariables = new Set();
  
  // 收集所有变量名
  envFiles.forEach(file => {
    if (file.variables) {
      file.variables.forEach((_, key) => {
        allVariables.add(key);
      });
    }
  });

  const sortedVariables = Array.from(allVariables).sort();
  const fileNames = envFiles.map(f => f.name);
  
  // CSV 内容
  const csvLines = [];
  
  // CSV 表头
  csvLines.push(['变量名', ...fileNames].join(','));
  
  // CSV 数据行
  for (const varName of sortedVariables) {
    const row = [varName];
    
    fileNames.forEach(fileName => {
      const file = envFiles.find(f => f.name === fileName);
      if (!file || !file.variables) {
        row.push(''); // 文件不存在，留空
        return;
      }
      
      const variable = file.variables.get(varName);
      if (!variable) {
        row.push(''); // 变量不存在，留空
      } else if (variable.isEmpty) {
        row.push(''); // 变量为空值，留空
      } else {
        // 处理包含逗号的值，用双引号包围
        let value = variable.value;
        if (value.includes(',') || value.includes('"') || value.includes('\n')) {
          value = '"' + value.replace(/"/g, '""') + '"';
        }
        row.push(value);
      }
    });
    
    csvLines.push(row.join(','));
  }
  
  // 添加统计信息
  csvLines.push(''); // 空行
  csvLines.push(['统计信息'].concat(Array(fileNames.length).fill('')).join(','));
  
  const statsRow = ['完整性'];
  fileNames.forEach(fileName => {
    const file = envFiles.find(f => f.name === fileName);
    if (!file || !file.variables) {
      statsRow.push('0%');
      return;
    }
    
    let missing = 0, total = 0;
    
    for (const varName of sortedVariables) {
      const variable = file.variables.get(varName);
      if (!variable) missing++;
      total++;
    }
    
    const present = total - missing;
    const percentage = total > 0 ? ((present / total) * 100).toFixed(1) : '0.0';
    statsRow.push(`${percentage}%`);
  });
  
  csvLines.push(statsRow.join(','));
  
  // 写入文件
  const csvContent = csvLines.join('\n');
  const csvPath = path.join(rootDir, 'env-check-report.csv');
  fs.writeFileSync(csvPath, csvContent, 'utf8');
  
  console.log(colorize(`📊 CSV 报告已生成: ${csvPath}`, 'blue'));
  return csvPath;
}

// 生成报告
function generateReport(envFiles, baseVariables) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: envFiles.length,
      baseFile: '.env.example',
      totalVariables: baseVariables.size
    },
    files: [],
    issues: {
      totalMissing: 0,
      totalEmpty: 0,
      totalExtra: 0
    }
  };

  const otherFiles = envFiles.filter(f => f.name !== '.env.example');
  
  for (const file of otherFiles) {
    if (!file.variables) continue;

    const missing = [];
    const extra = [];
    const empty = [];

    // 检查缺失的变量
    baseVariables.forEach((baseVar, key) => {
      if (!file.variables.has(key)) {
        missing.push(key);
      } else {
        const fileVar = file.variables.get(key);
        if (fileVar.isEmpty && !baseVar.isEmpty) {
          empty.push(key);
        }
      }
    });

    // 检查额外的变量
    file.variables.forEach((_, key) => {
      if (!baseVariables.has(key)) {
        extra.push(key);
      }
    });

    report.files.push({
      name: file.name,
      path: file.path,
      variableCount: file.variables.size,
      issues: {
        missing: missing,
        empty: empty,
        extra: extra
      }
    });

    report.issues.totalMissing += missing.length;
    report.issues.totalEmpty += empty.length;
    report.issues.totalExtra += extra.length;
  }

  const reportPath = path.join(rootDir, 'env-check-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
  
  console.log(colorize(`📊 报告已生成: ${reportPath}`, 'blue'));
  return report;
}

// 检查环境变量
function checkEnvironmentVariables() {
  if (options.help) {
    showHelp();
    return;
  }

  console.log(colorize('🔍 环境变量检查工具', 'bold'));
  console.log(colorize('=' .repeat(50), 'cyan'));
  console.log();

  const envFiles = getEnvFiles();
  
  if (envFiles.length === 0) {
    console.log(colorize('❌ 未找到任何环境变量文件', 'red'));
    return;
  }

  if (options.verbose) {
    console.log(colorize('📁 找到的环境变量文件:', 'blue'));
    envFiles.forEach(file => {
      const varCount = file.variables ? file.variables.size : 0;
      console.log(`  • ${file.name} (${varCount} 个变量)`);
    });
    console.log();
  }

  // 以 .env.example 作为基准
  const exampleFile = envFiles.find(f => f.name === '.env.example');
  if (!exampleFile) {
    console.log(colorize('⚠️  未找到 .env.example 文件，无法进行基准比较', 'yellow'));
    return;
  }

  const baseVariables = exampleFile.variables;
  const allVariables = new Set();

  // 收集所有变量名
  envFiles.forEach(file => {
    if (file.variables) {
      file.variables.forEach((_, key) => {
        allVariables.add(key);
      });
    }
  });

  console.log(colorize('📊 变量统计:', 'blue'));
  console.log(`  • 基准文件 (.env.example): ${baseVariables.size} 个变量`);
  console.log(`  • 所有文件总计: ${allVariables.size} 个不同变量`);
  console.log();

  // 显示表格格式的对比
  displayTable(envFiles, baseVariables);

  // 计算总问题数
  const otherFiles = envFiles.filter(f => f.name !== '.env.example');
  let totalIssues = 0;
  
  for (const file of otherFiles) {
    if (!file.variables) continue;

    const missing = [];
    const empty = [];

    // 检查缺失的变量和空值变量
    baseVariables.forEach((baseVar, key) => {
      if (!file.variables.has(key)) {
        missing.push(key);
      } else {
        const fileVar = file.variables.get(key);
        if (fileVar.isEmpty && !baseVar.isEmpty) {
          empty.push(key);
        }
      }
    });

    totalIssues += missing.length + empty.length;
  }

  // 自动修复
  if (options.fix && totalIssues > 0) {
    fixMissingVariables(envFiles, baseVariables);
    console.log();
  }

  // 生成报告
  if (options.report) {
    generateReport(envFiles, baseVariables);
    console.log();
  }

  // 生成CSV报告
  if (options.csv) {
    generateCSVReport(envFiles, baseVariables);
    console.log();
  }

  // 生成修复建议（仅在非修复模式下显示）
  if (!options.fix && totalIssues > 0) {
    console.log(colorize('💡 修复建议:', 'blue'));
    console.log('  运行 `pnpm checkenv --fix` 自动修复缺失的变量');
    console.log('  运行 `pnpm checkenv --report` 生成 JSON 报告');
    console.log('  运行 `pnpm checkenv --csv` 生成 CSV 报告');
    console.log();
  }

  if (totalIssues === 0) {
    console.log(colorize('🎉 所有文件都已正确配置!', 'green'));
  }

  console.log(colorize('=' .repeat(50), 'cyan'));
  console.log(colorize('检查完成!', 'bold'));
  
  // 返回退出码
  process.exit(totalIssues > 0 ? 1 : 0);
}

// 运行检查
checkEnvironmentVariables();