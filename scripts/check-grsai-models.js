#!/usr/bin/env node

/**
 * 检查GRSAI模型可用性的脚本
 * 用于定期验证配置的模型是否仍然可用
 */

const GRSAI_API_KEY = process.env.GRSAI_APIKEY;
const GRSAI_BASE_URL = 'https://api.grsai.com';

const TEST_MODELS = [
  'gemini-2.5-pro',
  'gemini-2.5-flash', 
  'gemini-2.5-flash-lite',
  'gpt-4o-mini',
  'gpt-4o-all',
  'o4-mini-all'
];

async function testModel(model) {
  try {
    const response = await fetch(`${GRSAI_BASE_URL}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${GRSAI_API_KEY}`
      },
      body: JSON.stringify({
        model: model,
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 10
      })
    });

    const data = await response.json();
    
    if (data.code === -1 && data.msg && data.msg.includes('不存在该模型')) {
      return { model, available: false, error: data.msg };
    }
    
    return { model, available: true };
  } catch (error) {
    return { model, available: false, error: error.message };
  }
}

async function checkAllModels() {
  console.log('🔍 检查GRSAI模型可用性...\n');
  
  const results = await Promise.all(TEST_MODELS.map(testModel));
  
  console.log('📊 检查结果:');
  console.log('=' .repeat(50));
  
  results.forEach(result => {
    const status = result.available ? '✅ 可用' : '❌ 不可用';
    console.log(`${result.model.padEnd(25)} ${status}`);
    if (!result.available && result.error) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  const unavailable = results.filter(r => !r.available);
  if (unavailable.length > 0) {
    console.log('\n⚠️  需要注意的模型:');
    unavailable.forEach(result => {
      console.log(`- ${result.model}: ${result.error}`);
    });
    
    console.log('\n💡 建议:');
    console.log('1. 在 services/provider/grsai/text-models.ts 中将不可用模型的 is_active 设为 false');
    console.log('2. 更新相关推荐逻辑，使用可用的替代模型');
    console.log('3. 定期运行此脚本检查模型状态');
  }
}

if (!GRSAI_API_KEY) {
  console.error('❌ 请设置 GRSAI_APIKEY 环境变量');
  process.exit(1);
}

checkAllModels().catch(console.error);