# Creem 订阅状态管理使用示例

## 1. 检查用户订阅权限

```typescript
import { checkUserSubscriptionAccess } from '@/services/user';

// 在需要验证用户权限的地方使用
async function checkAccess(userUuid: string) {
  const access = await checkUserSubscriptionAccess(userUuid);
  
  if (!access.hasAccess) {
    // 用户没有访问权限
    return { error: 'Subscription required' };
  }
  
  // 根据不同的访问类型显示不同的信息
  switch (access.accessType) {
    case 'active':
      console.log('用户订阅正常，到期时间:', access.expiresAt);
      break;
    case 'trial':
      console.log('用户在试用期，到期时间:', access.expiresAt);
      break;
    case 'grace_period':
      console.log('用户订阅已取消，但可使用到:', access.expiresAt);
      break;
  }
  
  return { success: true, access };
}
```

## 2. 获取用户活跃订阅信息

```typescript
import { getUserActiveSubscription } from '@/services/user';

async function getSubscriptionInfo(userUuid: string) {
  const subscription = await getUserActiveSubscription(userUuid);
  
  if (!subscription) {
    return { error: 'No active subscription found' };
  }
  
  return {
    subscriptionId: subscription.subscriptionId,
    status: subscription.status,
    productId: subscription.productId,
    credits: subscription.credits,
    periodEnd: subscription.periodEnd,
    canceledAt: subscription.canceledAt
  };
}
```

## 3. 订阅升级

```typescript
// 前端调用示例
async function upgradeSubscription(subscriptionId: string, newProductId: string) {
  try {
    const response = await fetch('/api/subscription/upgrade', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscriptionId,
        newProductId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('订阅升级成功');
      // 升级成功后，webhook 会自动处理积分补发
    } else {
      console.error('订阅升级失败:', result.error);
    }
  } catch (error) {
    console.error('升级请求失败:', error);
  }
}
```

## 4. 订阅取消

```typescript
// 前端调用示例
async function cancelSubscription(subscriptionId: string) {
  try {
    const response = await fetch('/api/subscription/cancel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscriptionId
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('订阅取消成功，可使用到当前周期结束');
      // 取消成功后，webhook 会自动更新订阅状态
    } else {
      console.error('订阅取消失败:', result.error);
    }
  } catch (error) {
    console.error('取消请求失败:', error);
  }
}
```

## 5. 在中间件中检查订阅权限

```typescript
// middleware.ts 或相关中间件文件
import { checkUserSubscriptionAccess } from '@/services/user';

export async function subscriptionMiddleware(userUuid: string) {
  const access = await checkUserSubscriptionAccess(userUuid);
  
  if (!access.hasAccess) {
    return {
      redirect: '/pricing', // 重定向到定价页面
      message: 'Please subscribe to continue using our services'
    };
  }
  
  // 如果是宽限期，显示提醒
  if (access.accessType === 'grace_period') {
    return {
      warning: `Your subscription was canceled. Access expires on ${access.expiresAt}`,
      showRenewalPrompt: true
    };
  }
  
  return { success: true };
}
```

## 6. 积分使用前的权限检查

```typescript
import { checkUserSubscriptionAccess } from '@/services/user';
import { getUserCredits } from '@/services/credit';

async function useAIService(userUuid: string, requiredCredits: number) {
  // 1. 检查订阅权限
  const access = await checkUserSubscriptionAccess(userUuid);
  if (!access.hasAccess) {
    return { error: 'Subscription required' };
  }
  
  // 2. 检查积分余额
  const userCredits = await getUserCredits(userUuid);
  if (userCredits.left_credits < requiredCredits) {
    return { error: 'Insufficient credits' };
  }
  
  // 3. 执行 AI 服务
  // ... AI 服务逻辑
  
  return { success: true };
}
```

## 7. 订阅状态变更通知

```typescript
// 可以在订阅状态处理函数中添加通知逻辑
import { sendEmail } from '@/services/email';

async function notifySubscriptionStatusChange(
  userEmail: string, 
  status: string, 
  expiresAt?: string
) {
  let subject: string;
  let message: string;
  
  switch (status) {
    case 'active':
      subject = 'Subscription Activated';
      message = 'Your subscription is now active!';
      break;
    case 'canceled':
      subject = 'Subscription Canceled';
      message = `Your subscription has been canceled. You can continue using our services until ${expiresAt}.`;
      break;
    case 'expired':
      subject = 'Subscription Expired';
      message = 'Your subscription has expired. Please renew to continue using our services.';
      break;
    default:
      return;
  }
  
  await sendEmail({
    to: userEmail,
    subject,
    message
  });
}
```

## 8. 订阅数据查询示例

```typescript
// 管理后台查询用户订阅信息
import { getSupabaseClient } from '@/models/db';

async function getSubscriptionStats() {
  const supabase = getSupabaseClient();
  
  // 查询各状态的订阅数量
  const { data: stats } = await supabase
    .from('orders')
    .select('sub_status, count(*)')
    .not('sub_id', 'is', null)
    .group('sub_status');
  
  // 查询即将过期的订阅
  const nextWeek = new Date();
  nextWeek.setDate(nextWeek.getDate() + 7);
  
  const { data: expiring } = await supabase
    .from('orders')
    .select('*')
    .eq('sub_status', 'active')
    .lt('sub_period_end', Math.floor(nextWeek.getTime() / 1000));
  
  return {
    stats,
    expiringSubscriptions: expiring
  };
}
```

## 9. 错误处理最佳实践

```typescript
import { handlePaymentCallback } from '@/services/order';

// Webhook 处理的错误恢复
async function processWebhookWithRetry(
  provider: string,
  sessionId: string,
  orderNo: string,
  eventType: string,
  maxRetries: number = 3
) {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await handlePaymentCallback(provider, sessionId, orderNo, eventType);
      console.log(`Webhook processed successfully on attempt ${attempt}`);
      return;
    } catch (error) {
      lastError = error as Error;
      console.error(`Webhook processing failed on attempt ${attempt}:`, error);
      
      if (attempt < maxRetries) {
        // 指数退避重试
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  // 所有重试都失败，记录错误并可能发送告警
  console.error(`Webhook processing failed after ${maxRetries} attempts:`, lastError);
  throw lastError;
}
```

这些示例展示了如何在实际应用中使用新实现的订阅状态管理功能。记住在生产环境中要添加适当的错误处理、日志记录和监控。