# 定价工具函数使用示例

## 概述

我们已经优化了积分配置系统，消除了重复配置。现在所有的产品信息和积分配置都统一存储在 `i18n/pages/pricing/en.json` 文件中，通过 `lib/pricing-utils.ts` 提供的工具函数来访问。

## 主要优化

### 之前的问题
- ❌ 重复配置：`config/credits-config.ts` 和 `i18n/pages/pricing/en.json` 都包含积分信息
- ❌ 数据不一致：两个文件可能出现不同步的情况
- ❌ 维护困难：需要同时更新多个文件

### 现在的解决方案
- ✅ 单一数据源：只使用 `i18n/pages/pricing/en.json`
- ✅ 数据一致性：所有积分信息来自同一个配置文件
- ✅ 易于维护：只需要更新一个文件

## 使用示例

### 1. 获取产品积分数量

```typescript
import { getProductCreditsFromPricing } from '@/lib/pricing-utils';

// 获取产品积分
const credits = getProductCreditsFromPricing('prod_78bRDLKVhrBlYr0pvhAv0e', 'creem');
console.log(`Product credits: ${credits}`); // 输出: Product credits: 100

// 在订阅处理中使用
async function processSubscription(productId: string) {
  const credits = getProductCreditsFromPricing(productId, 'creem');
  
  await increaseCredits({
    user_uuid: 'user_123',
    trans_type: CreditsTransType.OrderPay,
    credits: credits,
    expired_at: new Date().toISOString(),
    order_no: 'order_123'
  });
}
```

### 2. 获取产品完整信息

```typescript
import { getProductInfo } from '@/lib/pricing-utils';

const productInfo = getProductInfo('lite_monthly');
if (productInfo) {
  console.log(`Product: ${productInfo.title}`);
  console.log(`Credits: ${productInfo.credits}`);
  console.log(`Price: ${productInfo.price}`);
  console.log(`Interval: ${productInfo.interval}`);
}
```

### 3. 检查产品类型

```typescript
import { isSubscriptionProduct, isOneTimeProduct } from '@/lib/pricing-utils';

// 检查是否为订阅产品
if (isSubscriptionProduct('pro_monthly')) {
  console.log('This is a subscription product');
  // 处理订阅逻辑
} else if (isOneTimeProduct('small_package')) {
  console.log('This is a one-time purchase');
  // 处理一次性购买逻辑
}
```

### 4. 根据套餐和周期获取积分

```typescript
import { getCreditsByTierAndInterval } from '@/lib/pricing-utils';

// 获取不同套餐的积分
const liteMonthlyCredits = getCreditsByTierAndInterval('lite', 'month');
const proYearlyCredits = getCreditsByTierAndInterval('pro', 'year');

console.log(`Lite Monthly: ${liteMonthlyCredits} credits`);
console.log(`Pro Yearly: ${proYearlyCredits} credits`);
```

### 5. 获取产品有效期

```typescript
import { getProductValidMonths } from '@/lib/pricing-utils';

const validMonths = getProductValidMonths('small_package');
console.log(`Product valid for: ${validMonths} months`);

// 计算过期时间
const expiresAt = new Date();
expiresAt.setMonth(expiresAt.getMonth() + validMonths);
```

### 6. 在订阅升级中使用

```typescript
// services/order.ts 中的使用示例
import { getProductCreditsFromPricing } from '@/lib/pricing-utils';

async function handleSubscriptionUpdate(subscriptionData: any) {
  const subscriptionId = subscriptionData.id;
  const order = await findOrderBySubscriptionId(subscriptionId);
  
  if (!order) {
    throw new Error(`Order not found for updated subscription: ${subscriptionId}`);
  }

  const newProductId = subscriptionData.product?.id || subscriptionData.items?.[0]?.product_id;
  
  // 使用新的工具函数获取积分
  const newCredits = getProductCreditsFromPricing(newProductId, 'creem');
  const creditDiff = newCredits - order.credits;

  if (creditDiff > 0) {
    // 升级：补发积分差额
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.PlanUpgrade,
      credits: creditDiff,
      expired_at: expiredAt,
      order_no: order.order_no,
    });
  }
}
```

## 配置文件结构

### i18n/pages/pricing/en.json 中的产品配置

```json
{
  "pricing": {
    "items": [
      {
        "title": "Lite",
        "product_id": "lite_monthly",
        "credits": 100,
        "interval": "month",
        "valid_months": 1,
        "price": "$12.9"
      },
      {
        "title": "Pro",
        "product_id": "pro_yearly", 
        "credits": 2400,
        "interval": "year",
        "valid_months": 12,
        "price": "$19.9"
      }
    ]
  }
}
```

## 工具函数列表

| 函数名 | 用途 | 返回值 |
|--------|------|--------|
| `getProductCreditsFromPricing(productId, provider)` | 获取产品积分数量 | `number` |
| `getProductInfo(productId)` | 获取产品完整信息 | `PricingItem \| null` |
| `isSubscriptionProduct(productId)` | 检查是否为订阅产品 | `boolean` |
| `isOneTimeProduct(productId)` | 检查是否为一次性购买 | `boolean` |
| `getCreditsByTierAndInterval(tier, interval)` | 根据套餐和周期获取积分 | `number` |
| `getProductValidMonths(productId)` | 获取产品有效期（月数） | `number` |
| `getAllPricingItems()` | 获取所有定价配置 | `PricingItem[]` |

## 维护指南

### 添加新产品
1. 只需要在 `i18n/pages/pricing/en.json` 中添加新的产品配置
2. 确保包含必要的字段：`product_id`, `credits`, `interval`, `valid_months`
3. 工具函数会自动识别新产品

### 修改积分数量
1. 直接在 `i18n/pages/pricing/en.json` 中修改 `credits` 字段
2. 无需修改任何代码文件
3. 所有相关功能会自动使用新的积分配置

### 产品ID映射
工具函数支持多种产品ID格式的映射：
- 直接匹配：`lite_monthly` → 找到对应配置
- 基础ID匹配：通过 `product-config.ts` 映射到基础ID
- 模糊匹配：根据套餐名称（lite, pro, premium）进行匹配

这样的设计确保了即使产品ID格式发生变化，系统仍能正确获取积分信息。