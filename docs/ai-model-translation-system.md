# AI模型翻译系统架构文档

## 系统概述

AI模型翻译系统负责为GRSAI提供商的模型提供多语言支持，包括模型名称、描述和参数的翻译。

## 核心组件

### 1. 翻译文件结构
```
i18n/pages/AiModelTranslation/
├── en.json
├── zh.json
└── ...
```

翻译文件格式：
```json
{
  "models": {
    "grsai.gemini-2-5-pro": {
      "name": "Gemini 2.5 Pro",
      "description": "高级对话模型，适合复杂任务和专业用途",
      "parameters": {
        "max_tokens": {
          "description": "最大输出长度",
          "tooltip": "生成文本的最大token数量"
        }
      }
    }
  }
}
```

### 2. 模型配置 (services/provider/grsai/unified-models-textmulti.ts)
```typescript
export const GRSAI_UNIFIED_MODELS: UnifiedModelConfig[] = [
  {
    id: 'gemini-2.5-pro',
    name: '', // 空字符串，将从翻译文件获取
    translationKey: 'grsai.gemini-2-5-pro', // 关键字段
    // ... 其他配置
  }
];
```

### 3. 模型管理器 (services/provider/model-manager.ts)
```typescript
function convertGRSAIModelToUnified(model: UnifiedModelConfig, id: number): UnifiedModel {
  return {
    // ... 其他字段
    model_name: model.name, // 原始为空
    translationKey: model.translationKey, // 保留翻译key
  };
}
```

### 4. 前端翻译处理 (components/ai-dashboard/components/hooks/useAIGeneration.ts)

#### 核心翻译函数
```typescript
const applyTranslationsToModels = async (models: LocalAIModel[], locale: string) => {
  const translations = await getAiModelTranslation(locale);
  
  return models.map(model => {
    if (model.provider === 'grsai' && model.translationKey) {
      const modelTranslation = translations.models[model.translationKey];
      
      if (modelTranslation) {
        return {
          ...model,
          model_name: modelTranslation.name || model.model_name || model.model_id,
          description: modelTranslation.description || model.description
        };
      }
    }
    return model;
  });
};
```

#### 关键修复点
```typescript
const filterModels = () => {
  // ❌ 错误：重新获取未翻译数据
  // const filteredModels = modelType
  //   ? getLocalAIModelsByType(modelType)
  //   : allModels;

  // ✅ 正确：使用已翻译数据
  const filteredModels = modelType
    ? allModels.filter(model => model.model_type === modelType)
    : allModels;
};
```

## 数据流程

1. **模型配置加载**：从 unified-models-textmulti.ts 加载模型配置，包含 translationKey
2. **模型转换**：model-manager.ts 将配置转换为统一格式，保留 translationKey
3. **前端获取**：useAIGeneration.ts 调用 getLocalActiveAIModels() 获取模型列表
4. **翻译应用**：applyTranslationsToModels() 根据 translationKey 匹配翻译内容
5. **数据存储**：翻译后的模型存储在 allModels 状态中
6. **过滤显示**：filterModels() 从 allModels 中过滤，保持翻译内容

## 关键原则

### 1. 单一翻译点
- 翻译只在前端 useAIGeneration.ts 中进行
- 避免后端和前端同时翻译导致冲突

### 2. 数据一致性
- 使用 translationKey 确保准确匹配
- 所有过滤操作基于已翻译的 allModels

### 3. 错误处理
- 翻译失败时回退到原始内容
- 缺少翻译时使用 model_id 作为最后备选

## 常见问题

### 问题1：模型名称显示为空
**原因**：filterModels 重新获取未翻译数据
**解决**：使用 allModels.filter() 而不是 getLocalAIModelsByType()

### 问题2：翻译key不匹配
**原因**：模型ID与翻译key格式不一致
**解决**：使用模型的 translationKey 字段而不是构建key

### 问题3：翻译未生效
**原因**：后端本地化处理覆盖前端翻译
**解决**：移除后端 processModelWithLocalization 调用

## 扩展指南

### 添加新模型翻译
1. 在 unified-models-textmulti.ts 中设置正确的 translationKey
2. 在翻译文件中添加对应的翻译内容
3. 确保key格式一致：`grsai.model-id`

### 添加新语言支持
1. 创建新的翻译文件：`i18n/pages/AiModelTranslation/{locale}.json`
2. 复制英文翻译结构并翻译内容
3. 系统会自动支持新语言
