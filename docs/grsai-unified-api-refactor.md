# GRSAI 统一API重构文档

## 🎯 重构目标

解决API endpoint重复定义的问题，实现完全基于模型配置的统一架构。

## 📋 重构前的问题

### 1. 重复定义endpoint
- **模型配置中**：每个模型都有 `api_endpoint` 字段
- **API客户端中**：硬编码了endpoint（`/v1/chat/completions`, `/v1/draw/completions`, `/v1/draw/flux`, `/v1/video/veo`）

### 2. 不一致的架构
- 图像生成：使用模型配置选择API方法
- 文本生成：直接调用硬编码的方法
- 视频生成：直接调用硬编码的方法

### 3. 维护困难
- 添加新模型需要修改多个地方
- 修改endpoint需要同时更新配置和代码

## ✅ 重构后的架构

### 1. 统一的请求接口
```typescript
// 新增统一的请求类型
export interface GRSAIUnifiedRequest {
  model: string;
  prompt?: string;
  // 文本生成参数
  messages?: Array<{...}>;
  // 图像生成参数  
  size?: string;
  variants?: number;
  // 视频生成参数
  duration?: number;
  // 通用参数
  urls?: string[];
  webHook?: string;
  cdn?: string;
}
```

### 2. 统一的API方法
```typescript
// 新的统一方法
async makeGenerationRequest(
  endpoint: string, 
  request: GRSAIUnifiedRequest,
  options: { stream?: boolean } = {}
): Promise<GRSAIUnifiedResponse | ReadableStream>
```

### 3. 基于配置的路由
```typescript
// Service层现在使用模型配置
const model = getAIModelById(request.model);
const response = await this.grsaiProvider.makeGenerationRequest(
  model.api_endpoint,  // 直接使用模型配置的endpoint
  grsaiRequest
);
```

## 🔄 重构过程

### 第一阶段：修改API客户端
- ✅ 添加统一的请求/响应类型
- ✅ 实现 `makeGenerationRequest` 统一方法
- ✅ 标记旧方法为已弃用（保持向后兼容）

### 第二阶段：修改Service层
- ✅ 文本生成使用统一方法
- ✅ 图像生成使用统一方法  
- ✅ 视频生成使用统一方法
- ✅ 所有请求都基于模型配置的 `api_endpoint`

### 第三阶段：清理和标记
- ✅ 标记旧方法为已弃用
- ✅ 添加警告日志
- ✅ 保持向后兼容性

### 第四阶段：测试验证
- ✅ 创建测试脚本
- ✅ 验证endpoint路由正确性
- ✅ 确认配置一致性

## 🎉 重构收益

### 1. 消除重复定义
- ❌ 之前：endpoint在配置和代码中重复定义
- ✅ 现在：只在模型配置中定义，代码动态读取

### 2. 统一架构
- ❌ 之前：不同类型的生成使用不同的方法
- ✅ 现在：所有生成都使用统一的方法

### 3. 易于维护
- ❌ 之前：添加新模型需要修改多个文件
- ✅ 现在：只需要在模型配置中添加，代码自动适配

### 4. 配置驱动
- ❌ 之前：硬编码的endpoint选择逻辑
- ✅ 现在：完全基于模型配置的动态路由

## 📊 对比示例

### 重构前
```typescript
// API客户端 - 硬编码endpoint
async generateText(request: GRSAITextRequest) {
  const endpoint = '/v1/chat/completions';  // 硬编码
  return await this.makeRequest(endpoint, request);
}

// Service层 - 调用特定方法
const response = await this.grsaiProvider.generateText(grsaiRequest);
```

### 重构后
```typescript
// API客户端 - 接受endpoint参数
async makeGenerationRequest(endpoint: string, request: GRSAIUnifiedRequest) {
  return await this.makeRequest(endpoint, request);
}

// Service层 - 使用模型配置
const model = getAIModelById(request.model);
const response = await this.grsaiProvider.makeGenerationRequest(
  model.api_endpoint,  // 从配置读取
  grsaiRequest
);
```

## 🚀 使用方式

### 添加新模型
现在只需要在模型配置中添加：
```typescript
{
  model_id: 'new-model',
  model_type: 'text',
  provider: 'grsai',
  api_endpoint: '/v1/new/endpoint',  // 只需要在这里定义
  // ... 其他配置
}
```

代码会自动使用正确的endpoint，无需修改。

### 修改endpoint
只需要修改模型配置中的 `api_endpoint` 字段，代码会自动适配。

## 🔮 未来计划

### 短期
- [ ] 在生产环境中验证重构效果
- [ ] 监控日志确认endpoint使用正确
- [ ] 收集性能数据

### 长期
- [ ] 完全移除已弃用的方法（在确认稳定后）
- [ ] 考虑将其他提供商也采用类似的统一架构
- [ ] 进一步优化请求类型定义

## 📝 注意事项

1. **向后兼容**：旧的方法仍然可用，但会显示弃用警告
2. **日志监控**：新方法会输出详细的endpoint使用日志
3. **错误处理**：保持了原有的错误处理逻辑
4. **类型安全**：统一类型包含了所有可能的参数

## 🎯 验证方法

运行测试脚本验证重构效果：
```bash
export GRSAI_APIKEY="your_api_key"
node scripts/test-unified-grsai.js
```

检查日志输出确认使用了正确的endpoint：
```
[AI Service] Using model config endpoint: /v1/chat/completions for model: gemini-2.5-flash-lite
[GRSAI] Using unified method for endpoint: /v1/chat/completions
```

这个重构彻底解决了endpoint重复定义的问题，实现了真正的配置驱动架构！