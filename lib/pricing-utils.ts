// lib/pricing-utils.ts
import pricingConfig from '@/i18n/pages/pricing/en.json';
import { getProductConfigByProviderId } from '@/config/product-config';

// 定价配置中的产品项类型
interface PricingItem {
    product_id: string;
    credits: number;
    interval: string;
    title: string;
    price: string;
    valid_months?: number;
}

// 从定价配置中获取产品积分
export function getProductCreditsFromPricing(productId: string, provider: string = 'creem'): number {
    try {
        // 1. 首先尝试通过产品配置获取基础产品ID
        const productConfig = getProductConfigByProviderId(productId, provider);
        const baseProductId = productConfig?.base_id;

        // 2. 在定价配置中查找匹配的产品
        const pricingItem = pricingConfig.pricing.items.find((item: PricingItem) => {
            // 直接匹配产品ID或基础产品ID
            return item.product_id === productId || item.product_id === baseProductId;
        });

        if (pricingItem) {
            console.log(`Found credits for product ${productId}: ${pricingItem.credits}`);
            return pricingItem.credits;
        }

        // 3. 如果没找到，尝试通过产品名称模糊匹配
        const fallbackItem = pricingConfig.pricing.items.find((item: PricingItem) => {
            const itemId = item.product_id.toLowerCase();
            const searchId = (baseProductId || productId).toLowerCase();

            // 检查是否包含相同的套餐名称（lite, pro, premium）
            const tiers = ['lite', 'pro', 'premium', 'small'];
            for (const tier of tiers) {
                if (itemId.includes(tier) && searchId.includes(tier)) {
                    return true;
                }
            }
            return false;
        });

        if (fallbackItem) {
            console.log(`Found fallback credits for product ${productId}: ${fallbackItem.credits}`);
            return fallbackItem.credits;
        }

        console.warn(`No credits found for product: ${productId}, using default`);
        return 1000; // 默认积分数量
    } catch (error) {
        console.error('Failed to get product credits from pricing:', error);
        return 1000; // 默认积分数量
    }
}

// 获取所有定价配置
export function getAllPricingItems(): PricingItem[] {
    return pricingConfig.pricing.items;
}

// 根据套餐和周期获取积分数量
export function getCreditsByTierAndInterval(tier: string, interval: string): number {
    const item = pricingConfig.pricing.items.find((item: PricingItem) => {
        const itemId = item.product_id.toLowerCase();
        return itemId.includes(tier.toLowerCase()) &&
            (item.interval === interval ||
                (interval === 'month' && item.interval === 'month') ||
                (interval === 'year' && item.interval === 'year') ||
                (interval === 'one-time' && item.interval === 'one-time'));
    });

    return item ? item.credits : 1000;
}

// 获取产品的有效期（月数）
export function getProductValidMonths(productId: string): number {
    const item = pricingConfig.pricing.items.find((item: PricingItem) =>
        item.product_id === productId
    );

    return item?.valid_months || 1;
}

// 获取产品的完整信息
export function getProductInfo(productId: string): PricingItem | null {
    const item = pricingConfig.pricing.items.find((item: PricingItem) =>
        item.product_id === productId
    );

    return item || null;
}

// 检查产品是否为订阅类型
export function isSubscriptionProduct(productId: string): boolean {
    const item = getProductInfo(productId);
    return item ? item.interval !== 'one-time' : false;
}

// 检查产品是否为一次性购买
export function isOneTimeProduct(productId: string): boolean {
    const item = getProductInfo(productId);
    return item ? item.interval === 'one-time' : false;
}