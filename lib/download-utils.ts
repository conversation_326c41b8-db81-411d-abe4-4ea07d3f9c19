/**
 * 下载工具函数
 */

/**
 * 从URL获取文件名
 */
export function getFileNameFromUrl(url: string, fallbackName: string = 'download'): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const fileName = pathname.split('/').pop();

    if (fileName && fileName.includes('.')) {
      return fileName;
    }

    // 如果没有扩展名，尝试从URL推断
    const extension = getFileExtensionFromUrl(url);
    return `${fallbackName}.${extension}`;
  } catch (e) {
    return `${fallbackName}.jpg`;
  }
}

/**
 * 从URL获取文件扩展名
 */
export function getFileExtensionFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const extension = pathname.split('.').pop()?.toLowerCase();

    // 常见图片扩展名
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
    if (extension && imageExtensions.includes(extension)) {
      return extension;
    }

    return 'jpg'; // 默认为jpg
  } catch (e) {
    return 'jpg';
  }
}

/**
 * 通过 fetch 获取文件并触发浏览器下载
 * 支持跨域文件下载（需要正确的 CORS 配置）
 */
export async function downloadFileFromUrl(
  url: string,
  fileName?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // 如果没有提供文件名，从URL推断
    const finalFileName = fileName || getFileNameFromUrl(url, 'generated-image');

    console.log(`[Download] Starting download: ${url} -> ${finalFileName}`);

    // 通过 fetch 获取文件内容
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': '*/*',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取文件内容作为 blob
    const blob = await response.blob();

    // 创建 blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = finalFileName;

    // 不设置 target="_blank"，确保触发下载而不是打开新标签页
    link.style.display = 'none';

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理 blob URL
    URL.revokeObjectURL(blobUrl);

    console.log(`[Download] Successfully downloaded: ${finalFileName}`);

    return { success: true };
  } catch (error) {
    console.error('[Download] Failed to download file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 通过 API 代理下载文件（用于需要绕过 CORS 的情况）
 */
export async function downloadFileViaProxy(
  url: string,
  fileName?: string,
  onProgress?: (progress: number) => void
): Promise<{ success: boolean; error?: string }> {
  try {
    const finalFileName = fileName || getFileNameFromUrl(url, 'generated-file');

    console.log(`[Download] Starting proxy download: ${url} -> ${finalFileName}`);

    // 调用代理 API
    const response = await fetch('/api/download-proxy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ url, fileName: finalFileName }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = finalFileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 清理URL对象
    URL.revokeObjectURL(downloadUrl);

    console.log(`[Download] Successfully downloaded via proxy: ${finalFileName}`);

    return { success: true };
  } catch (error) {
    console.error('[Download] Failed to download file via proxy:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * 下载多个文件
 */
export async function downloadMultipleFiles(
  urls: string[],
  baseFileName?: string
): Promise<Array<{ url: string; success: boolean; error?: string }>> {
  const results = [];

  for (let i = 0; i < urls.length; i++) {
    const url = urls[i];
    const fileName = baseFileName
      ? `${baseFileName}_${i + 1}.${getFileExtensionFromUrl(url)}`
      : undefined;

    const result = await downloadFileFromUrl(url, fileName);

    results.push({
      url,
      ...result
    });

    // 添加小延迟避免同时下载太多文件
    if (i < urls.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  return results;
}
