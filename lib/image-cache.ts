import { cacheGet, cacheSet, cacheRemove } from './cache';
import { getTimestamp } from './time';

/**
 * 图片缓存工具
 * 结合 localStorage 和内存缓存，提供图片的持久化缓存
 */

// 内存缓存，用于存储 Blob 对象
const memoryCache = new Map<string, Blob>();

// 缓存键前缀
const CACHE_PREFIX = 'img_cache_';
const CACHE_EXPIRY_HOURS = 24; // 缓存24小时

/**
 * 生成缓存键
 */
function getCacheKey(url: string): string {
  return CACHE_PREFIX + btoa(url).replace(/[^a-zA-Z0-9]/g, '');
}

/**
 * 获取缓存过期时间戳
 */
function getExpiryTimestamp(): number {
  return getTimestamp() + (CACHE_EXPIRY_HOURS * 60 * 60);
}

/**
 * 将 Blob 转换为 base64 字符串
 */
function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // 移除 data:image/xxx;base64, 前缀
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * 将 base64 字符串转换为 Blob
 */
function base64ToBlob(base64: string, mimeType: string = 'image/png'): Blob {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

/**
 * 从缓存获取图片
 */
export async function getImageFromCache(url: string): Promise<Blob | null> {
  try {
    // 1. 首先检查内存缓存
    const memoryBlob = memoryCache.get(url);
    if (memoryBlob) {
      console.log('[ImageCache] Hit memory cache:', url);
      return memoryBlob;
    }

    // 2. 检查 localStorage 缓存
    const cacheKey = getCacheKey(url);
    const cachedData = cacheGet(cacheKey);
    
    if (cachedData) {
      console.log('[ImageCache] Hit localStorage cache:', url);
      
      // 解析缓存数据：mimeType|base64Data
      const [mimeType, base64Data] = cachedData.split('|');
      if (mimeType && base64Data) {
        const blob = base64ToBlob(base64Data, mimeType);
        
        // 同时存储到内存缓存
        memoryCache.set(url, blob);
        
        return blob;
      }
    }

    console.log('[ImageCache] Cache miss:', url);
    return null;
  } catch (error) {
    console.error('[ImageCache] Error getting from cache:', error);
    return null;
  }
}

/**
 * 将图片存储到缓存
 */
export async function setImageToCache(url: string, blob: Blob): Promise<void> {
  try {
    // 1. 存储到内存缓存
    memoryCache.set(url, blob);
    console.log('[ImageCache] Stored to memory cache:', url);

    // 2. 存储到 localStorage（如果文件不太大）
    const maxSize = 1024 * 1024; // 1MB 限制
    if (blob.size <= maxSize) {
      const base64Data = await blobToBase64(blob);
      const cacheData = `${blob.type}|${base64Data}`;
      
      const cacheKey = getCacheKey(url);
      const expiryTimestamp = getExpiryTimestamp();
      
      cacheSet(cacheKey, cacheData, expiryTimestamp);
      console.log('[ImageCache] Stored to localStorage cache:', url);
    } else {
      console.log('[ImageCache] File too large for localStorage, using memory cache only:', url);
    }
  } catch (error) {
    console.error('[ImageCache] Error setting cache:', error);
  }
}

/**
 * 从缓存中移除图片
 */
export function removeImageFromCache(url: string): void {
  try {
    // 从内存缓存移除
    memoryCache.delete(url);
    
    // 从 localStorage 移除
    const cacheKey = getCacheKey(url);
    cacheRemove(cacheKey);
    
    console.log('[ImageCache] Removed from cache:', url);
  } catch (error) {
    console.error('[ImageCache] Error removing from cache:', error);
  }
}

/**
 * 清理所有图片缓存
 */
export function clearImageCache(): void {
  try {
    // 清理内存缓存
    memoryCache.clear();
    
    // 清理 localStorage 中的图片缓存
    // 注意：这里只清理图片缓存，不影响其他缓存
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(CACHE_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
    
    console.log('[ImageCache] Cleared all image cache');
  } catch (error) {
    console.error('[ImageCache] Error clearing cache:', error);
  }
}

/**
 * 获取缓存统计信息
 */
export function getCacheStats(): {
  memoryCount: number;
  localStorageCount: number;
  totalSize: number;
} {
  const memoryCount = memoryCache.size;
  
  let localStorageCount = 0;
  let totalSize = 0;
  
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(CACHE_PREFIX)) {
        localStorageCount++;
        const value = localStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }
    });
  } catch (error) {
    console.error('[ImageCache] Error getting cache stats:', error);
  }
  
  return {
    memoryCount,
    localStorageCount,
    totalSize
  };
}