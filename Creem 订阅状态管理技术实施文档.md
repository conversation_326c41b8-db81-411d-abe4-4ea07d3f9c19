Creem 订阅状态管理技术实施文档
1. 数据库结构调整
1.1 Orders 表字段修改
-- 添加订阅状态字段（默认为空，付款后才设置状态）
ALTER TABLE orders ADD COLUMN sub_status VARCHAR(50) DEFAULT NULL;
ALTER TABLE orders ADD COLUMN sub_canceled_at TIMESTAMP NULL;

-- 添加索引优化查询性能
CREATE INDEX idx_orders_sub_status ON orders(sub_status);
CREATE INDEX idx_orders_sub_canceled ON orders(sub_canceled_at);
CREATE INDEX idx_orders_user_sub_status ON orders(user_uuid, sub_status);
1.2 字段说明
| 字段名 | 类型 | 默认值 | 说明 | |--------|------|--------|------| | sub_status | VARCHAR(50) | NULL | 订阅状态：null(未付款), active, canceled, expired, trialing | | sub_canceled_at | TIMESTAMP | NULL | 订阅取消时间，用于计算剩余使用期 |

重要： sub_status 默认为 NULL，只有在以下情况才设置值：

首次订阅付款成功 → active
开始试用期 → trialing
订阅被取消 → canceled
订阅过期 → expired
2. 事件处理逻辑
2.1 更新 CreemProvider.handleWebhook
// services/payment/creem.ts
async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
  try {
    const event = JSON.parse(body);
    console.log('[CREEM_WEBHOOK] Event type:', event.eventType);

    switch (event.eventType) {
      case 'checkout.completed':
        const hasSubscription = !!event.object.subscription;
        return {
          success: true,
          orderNo: event.object.metadata?.order_no,
          sessionId: event.object.id,
          eventType: 'checkout.completed',
          subscriptionData: hasSubscription ? event.object.subscription : null,
          isSubscription: hasSubscription
        };

      case 'subscription.paid':
        return {
          success: true,
          subscriptionId: event.object.id,
          customerId: event.object.customer.id,
          eventType: 'subscription.paid',
          subscriptionData: event.object,
          transactionId: event.object.last_transaction_id
        };

      case 'subscription.canceled':
        return {
          success: true,
          subscriptionId: event.object.id,
          eventType: 'subscription.canceled',
          subscriptionData: event.object,
          canceledAt: event.object.canceled_at
        };

      case 'subscription.expired':
        return {
          success: true,
          subscriptionId: event.object.id,
          eventType: 'subscription.expired',
          subscriptionData: event.object
        };

      case 'subscription.update':
        return {
          success: true,
          subscriptionId: event.object.id,
          eventType: 'subscription.update',
          subscriptionData: event.object
        };

      case 'subscription.trialing':
        return {
          success: true,
          subscriptionId: event.object.id,
          eventType: 'subscription.trialing',
          subscriptionData: event.object
        };

      default:
        return { success: true };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}
2.2 主处理函数更新
// services/order.ts
export async function handlePaymentCallback(
  provider: string,
  sessionId: string,
  orderNo?: string,
  eventType?: string,
  subscriptionData?: any,
  isSubscription?: boolean,
  transactionId?: string,
  additionalData?: any
) {
  try {
    console.log(`[PAYMENT_CALLBACK] Processing ${eventType} for ${provider}`);

    switch (eventType) {
      case 'checkout.completed':
        if (isSubscription) {
          // 首次订阅：忽略此事件，等待 subscription.paid
          console.log(`Ignoring checkout.completed for subscription: ${orderNo}`);
          return;
        } else {
          // 单次付款处理
          await handleOneTimePayment(provider, sessionId, orderNo, transactionId);
        }
        break;

      case 'subscription.paid':
        await handleSubscriptionPayment(subscriptionData, transactionId);
        break;

      case 'subscription.canceled':
        await handleSubscriptionCanceled(subscriptionData, additionalData?.canceledAt);
        break;

      case 'subscription.expired':
        await handleSubscriptionExpired(subscriptionData);
        break;

      case 'subscription.update':
        await handleSubscriptionUpdate(subscriptionData);
        break;

      case 'subscription.trialing':
        await handleSubscriptionTrialing(subscriptionData);
        break;

      default:
        console.log(`Unhandled event type: ${eventType}`);
    }
  } catch (error) {
    console.error(`Failed to process payment callback:`, error);
    throw error;
  }
}
3. 核心处理函数实现
3.1 订阅付款处理（含状态设置）
// services/order.ts
async function handleSubscriptionPayment(subscriptionData: any, transactionId?: string) {
  try {
    const subscriptionId = subscriptionData.id;
    const finalTransactionId = transactionId || subscriptionData.last_transaction_id;

    // 查找订单
    let order = await findOrderBySubscriptionId(subscriptionId);
    if (!order && subscriptionData.metadata?.order_no) {
      order = await findOrderByOrderNo(subscriptionData.metadata.order_no);
    }

    if (!order) {
      throw new Error(`Order not found for subscription: ${subscriptionId}`);
    }

    // 去重检查
    if (finalTransactionId) {
      const existingTransaction = await checkTransactionProcessed(order.order_no, finalTransactionId);
      if (existingTransaction) {
        console.log(`Transaction already processed: ${finalTransactionId}`);
        return;
      }
    }

    // 判断是否为首次订阅
    const isFirstSubscription = order.status === 'created';
    const periodStart = new Date(subscriptionData.current_period_start_date).getTime() / 1000;
    const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;
    const currentSubTimes = isFirstSubscription ? 1 : (order.sub_times || 0) + 1;

    console.log(`Processing subscription payment: ${subscriptionId}, isFirst: ${isFirstSubscription}`);

    // 更新订阅信息（包括设置 sub_status 为 active）
    await updateOrderSubscription(
      order.order_no,
      subscriptionId,
      1,
      periodStart,
      periodEnd,
      periodStart,
      'paid', // order status
      getIsoTimestr(),
      currentSubTimes,
      order.user_email,
      JSON.stringify({ ...subscriptionData, transaction_id: finalTransactionId }),
      'active' // 🔥 设置 sub_status 为 active
    );

    // 发放积分
    if (order.credits > 0) {
      const expiredAt = new Date(periodEnd * 1000).toISOString();
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.OrderPay,
        credits: order.credits,
        expired_at: expiredAt,
        order_no: order.order_no,
        transaction_id: finalTransactionId,
      });
    }

    // 首次订阅处理推荐奖励
    if (isFirstSubscription) {
      await updateAffiliateForOrder(order);
    }

    console.log(`Subscription payment processed: ${subscriptionId}`);
  } catch (error) {
    console.error('Failed to handle subscription payment:', error);
    throw error;
  }
}
3.2 订阅取消处理
// services/order.ts
async function handleSubscriptionCanceled(subscriptionData: any, canceledAt?: string) {
  try {
    const subscriptionId = subscriptionData.id;
    const order = await findOrderBySubscriptionId(subscriptionId);
    
    if (!order) {
      throw new Error(`Order not found for canceled subscription: ${subscriptionId}`);
    }

    console.log(`Processing subscription cancellation: ${subscriptionId}`);

    const cancelTime = canceledAt || new Date().toISOString();
    
    // 更新订阅状态为 canceled
    await updateSubscriptionStatus(
      order.order_no,
      'canceled',
      cancelTime,
      JSON.stringify({ ...subscriptionData, event: 'canceled' })
    );

    console.log(`Subscription canceled: ${subscriptionId}, user can use until period end`);
  } catch (error) {
    console.error('Failed to handle subscription cancellation:', error);
    throw error;
  }
}
3.3 订阅过期处理
// services/order.ts
async function handleSubscriptionExpired(subscriptionData: any) {
  try {
    const subscriptionId = subscriptionData.id;
    const order = await findOrderBySubscriptionId(subscriptionId);
    
    if (!order) {
      throw new Error(`Order not found for expired subscription: ${subscriptionId}`);
    }

    console.log(`Processing subscription expiration: ${subscriptionId}`);

    // 更新订阅状态为 expired
    await updateSubscriptionStatus(
      order.order_no,
      'expired',
      new Date().toISOString(),
      JSON.stringify({ ...subscriptionData, event: 'expired' })
    );

    console.log(`Subscription expired: ${subscriptionId}`);
  } catch (error) {
    console.error('Failed to handle subscription expiration:', error);
    throw error;
  }
}
3.4 订阅更新处理（升级/降级）
// services/order.ts
async function handleSubscriptionUpdate(subscriptionData: any) {
  try {
    const subscriptionId = subscriptionData.id;
    const order = await findOrderBySubscriptionId(subscriptionId);
    
    if (!order) {
      throw new Error(`Order not found for updated subscription: ${subscriptionId}`);
    }

    console.log(`Processing subscription update: ${subscriptionId}`);

    const newProductId = subscriptionData.product?.id || subscriptionData.items?.[0]?.product_id;
    const newCredits = await getProductCredits(newProductId);
    const creditDiff = newCredits - order.credits;

    // 只处理升级的积分补发（降级不回收积分）
    if (creditDiff > 0) {
      const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;
      const expiredAt = new Date(periodEnd * 1000).toISOString();
      
      await increaseCredits({
        user_uuid: order.user_uuid,
        trans_type: CreditsTransType.PlanUpgrade,
        credits: creditDiff,
        expired_at: expiredAt,
        order_no: order.order_no,
        remark: `Plan upgrade: ${order.credits} -> ${newCredits} credits`
      });

      console.log(`Plan upgrade credits granted: +${creditDiff} credits`);
    }

    // 更新订单的产品信息
    await updateOrderProduct(order.order_no, newProductId, newCredits);

    console.log(`Subscription update processed: ${subscriptionId}`);
  } catch (error) {
    console.error('Failed to handle subscription update:', error);
    throw error;
  }
}
3.5 试用期处理（可选）
// services/order.ts
const TRIAL_CREDITS = 100; // 固定试用积分

async function handleSubscriptionTrialing(subscriptionData: any) {
  try {
    const subscriptionId = subscriptionData.id;
    
    let order = await findOrderBySubscriptionId(subscriptionId);
    if (!order && subscriptionData.metadata?.order_no) {
      order = await findOrderByOrderNo(subscriptionData.metadata.order_no);
    }

    if (!order) {
      console.log(`No order found for trialing subscription: ${subscriptionId}`);
      return;
    }

    console.log(`Processing subscription trial: ${subscriptionId}`);

    // 更新订阅状态为 trialing
    await updateSubscriptionStatus(
      order.order_no,
      'trialing',
      new Date().toISOString(),
      JSON.stringify({ ...subscriptionData, event: 'trialing' })
    );

    // 发放固定试用积分
    const trialEnd = new Date(subscriptionData.current_period_end_date).toISOString();
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.Trial,
      credits: TRIAL_CREDITS,
      expired_at: trialEnd,
      order_no: order.order_no,
      remark: 'Trial period credits'
    });

    console.log(`Trial started: ${subscriptionId}, ${TRIAL_CREDITS} credits granted`);
  } catch (error) {
    console.error('Failed to handle subscription trial:', error);
    throw error;
  }
}
4. 数据库操作函数
4.1 更新 updateOrderSubscription 函数
// models/order.ts
export async function updateOrderSubscription(
  orderNo: string,
  subId: string,
  subIntervalCount: number,
  subCycleAnchor: number,
  subPeriodEnd: number,
  subPeriodStart: number,
  status: string,
  paidAt: string,
  subTimes: number,
  paidEmail: string,
  paidDetail: string,
  subStatus: string // 🔥 新增参数：订阅状态
): Promise<void> {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from("orders")
    .update({
      sub_id: subId,
      sub_interval_count: subIntervalCount,
      sub_cycle_anchor: subCycleAnchor,
      sub_period_end: subPeriodEnd,
      sub_period_start: subPeriodStart,
      status: status,
      paid_at: paidAt,
      sub_times: subTimes,
      paid_email: paidEmail,
      paid_detail: paidDetail,
      sub_status: subStatus, // 🔥 设置订阅状态
      updated_at: new Date().toISOString()
    })
    .eq("order_no", orderNo);

  if (error) {
    throw new Error(`Failed to update order subscription: ${error.message}`);
  }
}
4.2 新增订阅状态更新函数
// models/order.ts
export async function updateSubscriptionStatus(
  orderNo: string,
  subStatus: string,
  statusChangedAt: string,
  paidDetail: string
): Promise<void> {
  const supabase = getSupabaseClient();
  
  const updateData: any = {
    sub_status: subStatus,
    paid_detail: paidDetail,
    updated_at: new Date().toISOString()
  };

  // 如果是取消状态，记录取消时间
  if (subStatus === 'canceled') {
    updateData.sub_canceled_at = statusChangedAt;
  }

  const { error } = await supabase
    .from("orders")
    .update(updateData)
    .eq("order_no", orderNo);

  if (error) {
    throw new Error(`Failed to update subscription status: ${error.message}`);
  }
}

export async function updateOrderProduct(
  orderNo: string,
  productId: string,
  credits: number
): Promise<void> {
  const supabase = getSupabaseClient();
  
  const { error } = await supabase
    .from("orders")
    .update({
      product_id: productId,
      credits: credits,
      updated_at: new Date().toISOString()
    })
    .eq("order_no", orderNo);

  if (error) {
    throw new Error(`Failed to update order product: ${error.message}`);
  }
}
5. 用户权限检查
5.1 统一权限检查函数
// services/user.ts
export async function checkUserSubscriptionAccess(userUuid: string): Promise<{
  hasAccess: boolean;
  accessType: 'active' | 'trial' | 'grace_period' | 'expired';
  expiresAt?: string;
  subscriptionStatus?: string;
}> {
  try {
    const supabase = getSupabaseClient();
    
    // 查找用户的订阅订单
    const { data: orders, error } = await supabase
      .from("orders")
      .select("*")
      .eq("user_uuid", userUuid)
      .not("sub_id", "is", null)
      .in("sub_status", ["active", "trialing", "canceled"])
      .order("created_at", { ascending: false });

    if (error || !orders || orders.length === 0) {
      return { hasAccess: false, accessType: 'expired' };
    }

    const latestOrder = orders[0];
    const now = new Date();
    const periodEnd = new Date(latestOrder.sub_period_end * 1000);

    switch (latestOrder.sub_status) {
      case 'active':
        return {
          hasAccess: true,
          accessType: 'active',
          expiresAt: periodEnd.toISOString(),
          subscriptionStatus: 'active'
        };

      case 'trialing':
        return {
          hasAccess: true,
          accessType: 'trial',
          expiresAt: periodEnd.toISOString(),
          subscriptionStatus: 'trialing'
        };

      case 'canceled':
        // 取消的订阅可以使用到周期结束
        if (now < periodEnd) {
          return {
            hasAccess: true,
            accessType: 'grace_period',
            expiresAt: periodEnd.toISOString(),
            subscriptionStatus: 'canceled'
          };
        } else {
          return {
            hasAccess: false,
            accessType: 'expired',
            subscriptionStatus: 'canceled'
          };
        }

      default:
        return { hasAccess: false, accessType: 'expired' };
    }
  } catch (error) {
    console.error('Failed to check user subscription access:', error);
    return { hasAccess: false, accessType: 'expired' };
  }
}
6. Webhook 路由更新
// app/api/payment-notify/[provider]/route.ts
export async function POST(req: Request, { params }: { params: Promise<{ provider: string }> }) {
  try {
    const { provider } = await params;
    const body = await req.text();
    const signature = req.headers.get('x-creem-signature') || '';

    const paymentProvider = PaymentFactory.createProvider(provider);
    const result = await paymentProvider.handleWebhook(body, signature);

    if (result.success) {
      switch (result.eventType) {
        case 'checkout.completed':
          if (result.orderNo && result.sessionId) {
            await handlePaymentCallback(
              provider,
              result.sessionId,
              result.orderNo,
              'checkout.completed',
              result.subscriptionData,
              result.isSubscription,
              result.transactionId
            );
          }
          break;

        case 'subscription.paid':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.paid',
            result.subscriptionData,
            false,
            result.transactionId
          );
          break;

        case 'subscription.canceled':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.canceled',
            result.subscriptionData,
            false,
            undefined,
            { canceledAt: result.canceledAt }
          );
          break;

        case 'subscription.expired':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.expired',
            result.subscriptionData
          );
          break;

        case 'subscription.update':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.update',
            result.subscriptionData
          );
          break;

        case 'subscription.trialing':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.trialing',
            result.subscriptionData
          );
          break;
      }
    }

    return respOk();
  } catch (error) {
    console.error(`Webhook failed:`, error);
    return respErr(`webhook failed: ${error.message}`);
  }
}
7. 升级/降级 API 实现
7.1 用户升级 API
// app/api/subscription/upgrade/route.ts
export async function POST(req: Request) {
  try {
    const { subscriptionId, newProductId } = await req.json();
    
    // 调用 Creem API 升级订阅
    const response = await axios.post(
      `https://api.creem.io/v1/subscriptions/${subscriptionId}/upgrade`,
      {
        product_id: newProductId,
        update_behavior: "proration-charge-immediately" // 立即生效并按比例计费
      },
      {
        headers: { "x-api-key": process.env.CREEM_API_KEY }
      }
    );

    return NextResponse.json({ success: true, data: response.data });
  } catch (error) {
    console.error('Upgrade failed:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
7.2 用户降级 API
// app/api/subscription/downgrade/route.ts
export async function POST(req: Request) {
  try {
    const { subscriptionId, newProductId } = await req.json();
    
    // 调用 Creem API 降级订阅
    const response = await axios.post(
      `https://api.creem.io/v1/subscriptions/${subscriptionId}/upgrade`,
      {
        product_id: newProductId,
        update_behavior: "proration-none" // 下个周期生效，不退款
      },
      {
        headers: { "x-api-key": process.env.CREEM_API_KEY }
      }
    );

    return NextResponse.json({ success: true, data: response.data });
  } catch (error) {
    console.error('Downgrade failed:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
8. 实施检查清单
8.1 数据库检查
[ ] 执行 SQL 添加 sub_status 和 sub_canceled_at 字段
[ ] 创建必要的索引
[ ] 验证现有订单的 sub_status 为 NULL
8.2 代码更新检查
[ ] 更新 CreemProvider.handleWebhook 方法
[ ] 更新 handlePaymentCallback 主函数
[ ] 实现所有订阅状态处理函数
[ ] 更新 updateOrderSubscription 函数签名
[ ] 添加新的数据库操作函数
8.3 测试验证
[ ] 测试首次订阅付款（sub_status 应设为 active）
[ ] 测试订阅取消（sub_status 应设为 canceled）
[ ] 测试订阅过期（sub_status 应设为 expired）
[ ] 测试订阅升级（积分应正确补发）
[ ] 测试用户权限检查函数
8.4 监控设置
[ ] 添加订阅状态变更日志
[ ] 监控 webhook 处理成功率
[ ] 设置异常报警机制
9. 关键注意事项
9.1 状态管理原则
NULL 状态：订单创建时的初始状态，表示还未付款
active 状态：订阅付款成功后设置
canceled 状态：用户可继续使用到周期结束
expired 状态：订阅真正过期，用户失去访问权限
9.2 积分策略
单次付款：积分永不过期
订阅付款：积分在当前周期结束时过期
升级：立即补发积分差额
降级：不回收已发放积分
试用：固定积分数量，试用期结束时过期
9.3 错误处理
所有 webhook 处理函数都应该有完善的错误处理
数据库操作失败不应影响其他业务逻辑
记录详细的错误日志便于调试
这个技术文档提供了完整的实施指导，你可以按照检查清单逐步实施。有什么问题随时问我！

