// 简单的测试脚本来验证重构的基本逻辑

console.log('Testing AI Dashboard refactor...');

// 测试 reducer
const { aiGenerationReducer, initialState } = require('./components/ai-dashboard/components/ai-generation.reducer.ts');

// 测试初始状态
console.log('Initial state:', JSON.stringify(initialState, null, 2));

// 测试 action
const testAction = {
  type: 'SET_FORM_DATA',
  payload: { prompt: 'Test prompt' }
};

try {
  const newState = aiGenerationReducer(initialState, testAction);
  console.log('Reducer test passed');
  console.log('New state prompt:', newState.formData.prompt);
} catch (error) {
  console.error('Reducer test failed:', error.message);
}

console.log('Basic refactor structure looks good!');