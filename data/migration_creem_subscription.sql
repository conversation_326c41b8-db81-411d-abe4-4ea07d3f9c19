-- Creem订阅与单次付款数据库迁移脚本
-- 执行前请备份数据库
-- 注意：orders表的payment_session_id和payment_provider字段已存在，跳过相关操作

BEGIN;

-- 1. 检查并更新orders表（如果需要）
-- payment_provider和payment_session_id字段已存在，无需修改

-- 2. 更新credits表结构
-- 添加transaction_id字段用于去重
ALTER TABLE credits ADD COLUMN IF NOT EXISTS transaction_id VARCHAR(255);

-- 3. 添加索引优化查询性能

-- 订阅查询索引
CREATE INDEX IF NOT EXISTS idx_orders_sub_id ON orders(sub_id);
CREATE INDEX IF NOT EXISTS idx_orders_user_subscription ON orders(user_uuid, sub_id);
CREATE INDEX IF NOT EXISTS idx_orders_subscription_status ON orders(sub_id, status);

-- 支付会话查询索引
CREATE INDEX IF NOT EXISTS idx_orders_payment_session ON orders(payment_session_id, payment_provider);

-- 用户订单查询索引
CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_uuid, status);
CREATE INDEX IF NOT EXISTS idx_orders_user_email_status ON orders(user_email, status);

-- 积分查询索引
CREATE INDEX IF NOT EXISTS idx_credits_user_expired ON credits(user_uuid, expired_at);
CREATE INDEX IF NOT EXISTS idx_credits_order_transaction ON credits(order_no, transaction_id);

-- 4. 可选：添加订阅状态字段（如果需要）
-- ALTER TABLE orders ADD COLUMN IF NOT EXISTS sub_status VARCHAR(50);

COMMIT;

-- 验证迁移结果（使用简单查询验证）
DO $$
BEGIN
    -- 验证transaction_id字段是否添加成功
    PERFORM 1 FROM information_schema.columns
    WHERE table_name = 'credits' AND column_name = 'transaction_id';

    IF FOUND THEN
        RAISE NOTICE '✅ credits表transaction_id字段添加成功';
    ELSE
        RAISE NOTICE '❌ credits表transaction_id字段添加失败';
    END IF;

    RAISE NOTICE '✅ 数据库迁移完成';
    RAISE NOTICE '📊 可以使用以下查询验证结果：';
    RAISE NOTICE 'SELECT COUNT(*) FROM orders WHERE payment_provider IS NOT NULL;';
    RAISE NOTICE 'SELECT COUNT(*) FROM credits WHERE transaction_id IS NOT NULL;';
END $$;
