-- Creem 订阅状态管理数据库迁移脚本
-- 执行前请备份数据库

BEGIN;

-- 1. 添加订阅状态字段（默认为空，付款后才设置状态）
ALTER TABLE orders ADD COLUMN IF NOT EXISTS sub_status VARCHAR(50) DEFAULT NULL;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS sub_canceled_at TIMESTAMP NULL;

-- 2. 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_orders_sub_status ON orders(sub_status);
CREATE INDEX IF NOT EXISTS idx_orders_sub_canceled ON orders(sub_canceled_at);
CREATE INDEX IF NOT EXISTS idx_orders_user_sub_status ON orders(user_uuid, sub_status);

-- 3. 验证字段添加成功
DO $$
BEGIN
    -- 验证sub_status字段是否添加成功
    PERFORM 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'sub_status';

    IF FOUND THEN
        RAISE NOTICE '✅ orders表sub_status字段添加成功';
    ELSE
        RAISE NOTICE '❌ orders表sub_status字段添加失败';
    END IF;

    -- 验证sub_canceled_at字段是否添加成功
    PERFORM 1 FROM information_schema.columns
    WHERE table_name = 'orders' AND column_name = 'sub_canceled_at';

    IF FOUND THEN
        RAISE NOTICE '✅ orders表sub_canceled_at字段添加成功';
    ELSE
        RAISE NOTICE '❌ orders表sub_canceled_at字段添加失败';
    END IF;

    RAISE NOTICE '✅ 订阅状态管理数据库迁移完成';
END $$;

COMMIT;

-- 字段说明：
-- sub_status: 订阅状态
--   - NULL: 未付款（默认状态）
--   - 'active': 订阅激活
--   - 'canceled': 订阅已取消（用户可使用到周期结束）
--   - 'expired': 订阅已过期
--   - 'trialing': 试用期
-- sub_canceled_at: 订阅取消时间，用于计算剩余使用期