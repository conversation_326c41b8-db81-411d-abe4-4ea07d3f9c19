-- ========================================
-- 完整数据库初始化脚本
-- 适用于新项目快速启动
-- ========================================

-- 基础用户和系统表
-- ========================================

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    nickname VARCHAR(255),
    avatar_url VARCHAR(255),
    locale VARCHAR(50) DEFAULT 'en',
    signin_type VARCHAR(50),
    signin_ip VARCHAR(255),
    signin_provider VARCHAR(50),
    signin_openid VARCHAR(255),
    invite_code VARCHAR(255) NOT NULL DEFAULT '',
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    invited_by VARCHAR(255) NOT NULL DEFAULT '',
    is_affiliate BOOLEAN NOT NULL DEFAULT false,
    UNIQUE (email, signin_provider)
);

CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) NOT NULL DEFAULT '',
    amount INT NOT NULL,
    interval VARCHAR(50),
    expired_at TIMESTAMPTZ,
    status VARCHAR(50) NOT NULL,
    payment_session_id VARCHAR(255),
    credits INT NOT NULL,
    currency VARCHAR(50) DEFAULT 'USD',
    sub_id VARCHAR(255),
    sub_interval_count INT,
    sub_cycle_anchor INT,
    sub_period_end INT,
    sub_period_start INT,
    sub_times INT,
    product_id VARCHAR(255),
    product_name VARCHAR(255),
    valid_months INT,
    order_detail TEXT,
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT,
    payment_provider VARCHAR(50) NOT NULL DEFAULT 'stripe',
    provider_specific_data JSONB
);

CREATE TABLE apikeys (
    id SERIAL PRIMARY KEY,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(100),
    user_uuid VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active'
);

CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,
    order_no VARCHAR(255),
    expired_at TIMESTAMPTZ,
    model_id VARCHAR(100),
    usage_id INT,
    request_id VARCHAR(255),
     transaction_id VARCHAR(255)
);

CREATE TABLE posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'draft',
    cover_url VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50) DEFAULT 'en'
);

CREATE TABLE affiliates (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    invited_by VARCHAR(255) NOT NULL,
    paid_order_no VARCHAR(255) NOT NULL DEFAULT '',
    paid_amount INT NOT NULL DEFAULT 0,
    reward_percent INT NOT NULL DEFAULT 10,
    reward_amount INT NOT NULL DEFAULT 0
);

CREATE TABLE feedbacks (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'pending',
    user_uuid VARCHAR(255),
    content TEXT,
    rating INT CHECK (rating >= 1 AND rating <= 5)
);

-- AI 模型使用记录表
-- ========================================

CREATE TABLE ai_model_usage (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    model_id VARCHAR(100) NOT NULL,
    task_id VARCHAR(255) UNIQUE NOT NULL, -- 统一的任务ID，用于前端查询
    external_request_id VARCHAR(255), -- 外部提供商的任务ID (GRSAI/Replicate等)
    provider VARCHAR(50), -- 提供商：grsai, replicate等
    input_size INT, -- 输入大小（tokens/pixels等）
    output_size INT, -- 输出大小
    credits_consumed INT NOT NULL, -- 实际消耗的积分
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, success, failed, cancelled
    error_reason VARCHAR(100), -- 错误原因
    error_detail TEXT, -- 详细错误信息
    request_params JSONB, -- 请求参数
    response_data JSONB, -- 响应数据
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
-- ========================================

-- 基础表索引
CREATE INDEX idx_users_uuid ON users(uuid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_uuid ON orders(user_uuid);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_apikeys_user_uuid ON apikeys(user_uuid);
CREATE INDEX idx_credits_user_uuid ON credits(user_uuid);
CREATE INDEX idx_credits_model_id ON credits(model_id);
CREATE INDEX idx_credits_request_id ON credits(request_id);
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_locale ON posts(locale);



CREATE INDEX idx_ai_model_usage_user ON ai_model_usage(user_uuid);
CREATE INDEX idx_ai_model_usage_model ON ai_model_usage(model_id);
CREATE INDEX idx_ai_model_usage_status ON ai_model_usage(status);
CREATE INDEX idx_ai_model_usage_created ON ai_model_usage(created_at);

-- 外键约束
-- ========================================
-- ai_models表已移除，无需外键约束

-- 实用函数
-- ========================================

-- 获取本地化内容的函数
CREATE OR REPLACE FUNCTION get_localized_content(
  content JSONB,
  locale VARCHAR(5) DEFAULT 'en',
  fallback VARCHAR(5) DEFAULT 'zh'
) RETURNS TEXT AS $$
BEGIN
  IF content IS NULL THEN
    RETURN '';
  END IF;

  IF content ? locale THEN
    RETURN content ->> locale;
  END IF;

  IF content ? fallback THEN
    RETURN content ->> fallback;
  END IF;

  RETURN (SELECT value FROM jsonb_each_text(content) LIMIT 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 模型成本计算已移至应用层处理

-- 自动更新 updated_at 的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为相关表创建触发器
CREATE TRIGGER update_posts_updated_at
    BEFORE UPDATE ON posts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();



-- 实用视图
-- ========================================

-- 用户积分使用统计视图（简化版，不依赖ai_models表）
CREATE OR REPLACE VIEW user_credits_usage_stats AS
SELECT
    u.user_uuid,
    u.model_id,
    COUNT(*) as usage_count,
    SUM(u.credits_consumed) as total_credits_consumed,
    AVG(u.credits_consumed) as avg_credits_per_use,
    COUNT(CASE WHEN u.status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN u.status = 'failed' THEN 1 END) as failed_count,
    MAX(u.created_at) as last_used_at
FROM ai_model_usage u
GROUP BY u.user_uuid, u.model_id;

-- 模型使用统计视图（简化版，不依赖ai_models表）
CREATE OR REPLACE VIEW model_usage_stats AS
SELECT
    u.model_id,
    u.provider,
    COUNT(u.id) as total_usage_count,
    COUNT(DISTINCT u.user_uuid) as unique_users,
    SUM(u.credits_consumed) as total_credits_consumed,
    AVG(u.credits_consumed) as avg_credits_per_use,
    COUNT(CASE WHEN u.status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN u.status = 'failed' THEN 1 END) as failed_count,
    ROUND(COUNT(CASE WHEN u.status = 'success' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate
FROM ai_model_usage u
GROUP BY u.model_id, u.provider
ORDER BY total_usage_count DESC;


-- 初始化完成
-- ========================================
-- 数据库初始化完成！
-- 支持：多语言、积分系统、用户管理、订单系统
-- 新项目可以直接使用此脚本快速启动
