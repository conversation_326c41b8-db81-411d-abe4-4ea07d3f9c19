import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { newStorage } from "@/lib/storage";
import { NextRequest } from "next/server";

interface BatchUploadItem {
  base64Data: string;
  filename: string;
  contentType: string;
}

interface BatchUploadRequest {
  images: BatchUploadItem[];
}

export async function POST(req: NextRequest) {
  try {
    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    console.log(`[Batch Image Upload] User ${user_uuid} uploading images`);

    const requestData: BatchUploadRequest = await req.json();
    const { images } = requestData;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return respErr("No images provided");
    }

    console.log(`[Batch Image Upload] Processing ${images.length} images`);

    const uploadResults = [];
    const storage = newStorage();

    for (let i = 0; i < images.length; i++) {
      const image = images[i];

      try {
        // 验证 base64 数据
        if (!image.base64Data || !image.base64Data.startsWith('data:image/')) {
          console.error(`[Batch Image Upload] Invalid base64 data for image ${i}`);
          uploadResults.push({
            success: false,
            error: "Invalid base64 data",
            index: i
          });
          continue;
        }

        // 提取 base64 数据（去掉 data:image/xxx;base64, 前缀）
        const base64Match = image.base64Data.match(/^data:image\/[^;]+;base64,(.+)$/);
        if (!base64Match) {
          console.error(`[Batch Image Upload] Invalid base64 format for image ${i}`);
          uploadResults.push({
            success: false,
            error: "Invalid base64 format",
            index: i
          });
          continue;
        }

        const base64Content = base64Match[1];
        const buffer = Buffer.from(base64Content, 'base64');

        // 验证文件大小 (最大 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (buffer.length > maxSize) {
          console.error(`[Batch Image Upload] File size too large for image ${i}: ${buffer.length} bytes`);
          uploadResults.push({
            success: false,
            error: "File size too large. Maximum 10MB allowed",
            index: i
          });
          continue;
        }

        // 生成唯一文件名
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const fileExtension = image.filename.split('.').pop() || 'jpg';
        const fileName = `temp/ai-uploads/${user_uuid}/${timestamp}-${randomString}-${i}.${fileExtension}`;

        console.log(`[Batch Image Upload] Uploading image ${i}: ${fileName}, size: ${buffer.length}, type: ${image.contentType}`);

        // 上传到R2存储
        const uploadResult = await storage.uploadFile({
          body: buffer,
          key: fileName,
          contentType: image.contentType,
          disposition: 'inline'
        });

        console.log(`[Batch Image Upload] Upload successful for image ${i}:`, uploadResult);

        uploadResults.push({
          success: true,
          url: uploadResult.url,
          filename: uploadResult.filename,
          key: uploadResult.key,
          size: buffer.length,
          type: image.contentType,
          index: i
        });

      } catch (uploadError) {
        console.error(`[Batch Image Upload] Upload failed for image ${i}:`, uploadError);
        uploadResults.push({
          success: false,
          error: "Failed to upload image",
          index: i
        });
      }
    }

    // 统计结果
    const successCount = uploadResults.filter(result => result.success).length;
    const failureCount = uploadResults.length - successCount;

    console.log(`[Batch Image Upload] Batch upload completed: ${successCount} success, ${failureCount} failed`);

    return respData({
      results: uploadResults,
      summary: {
        total: images.length,
        success: successCount,
        failed: failureCount
      }
    });

  } catch (error) {
    console.error("Batch image upload error:", error);
    return respErr("Batch image upload failed");
  }
}
