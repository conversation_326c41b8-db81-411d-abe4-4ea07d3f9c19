import { PaymentFactory } from '@/services/payment/factory';
import { handlePaymentCallback } from '@/services/order';
import { respOk, respErr } from '@/lib/resp';
import { findOrderBySubscriptionId } from '@/models/order';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ provider: string }> }
) {
  try {
    const { provider } = await params;

    // 验证支付平台
    const enabledProviders = PaymentFactory.getEnabledProviders();
    if (!enabledProviders.includes(provider)) {
      return respErr(`Unsupported payment provider: ${provider}`);
    }

    const body = await req.text();
    const signature = req.headers.get(`${provider}-signature`) ||
                     req.headers.get('stripe-signature') ||
                     req.headers.get('x-creem-signature') || '';

    if (!signature || !body) {
      return respErr("invalid notify data");
    }

    // 使用对应的支付提供商处理webhook
    const paymentProvider = PaymentFactory.createProvider(provider);
    const result = await paymentProvider.handleWebhook(body, signature);

    if (result.success) {
      switch (result.eventType) {
        case 'checkout.completed':
          if (result.orderNo && result.sessionId) {
            await handlePaymentCallback(
              provider,
              result.sessionId,
              result.orderNo,
              'checkout.completed',
              result.subscriptionData,
              result.isSubscription,
              result.transactionId
            );
          }
          break;

        case 'subscription.paid':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.paid',
            result.subscriptionData,
            false,
            result.transactionId
          );
          break;

        case 'subscription.canceled':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.canceled',
            result.subscriptionData,
            false,
            undefined,
            { canceledAt: result.canceledAt }
          );
          break;

        case 'subscription.expired':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.expired',
            result.subscriptionData
          );
          break;

        case 'subscription.update':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.update',
            result.subscriptionData
          );
          break;

        case 'subscription.trialing':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.trialing',
            result.subscriptionData
          );
          break;

        case 'subscription.active':
          // 仅记录日志，不处理业务逻辑
          console.log('Subscription active event received for sync');
          break;

        default:
          // 兼容旧版本处理
          if (result.orderNo && result.sessionId) {
            await handlePaymentCallback(provider, result.sessionId, result.orderNo);
          }
          break;
      }
    }

    return respOk();
  } catch (e: any) {
    const { provider: providerName } = await params;
    console.log(`${providerName} webhook failed:`, e);
    return respErr(`webhook failed: ${e.message}`);
  }
}


