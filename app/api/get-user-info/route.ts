import { respData, respErr, respJson } from "@/lib/resp";

import { findUserByUuid } from "@/models/user";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { getOrdersByUserUuid } from "@/models/order";
import { getCreditsByUserUuid } from "@/models/credit";
import { getUserAffiliates, getAffiliateSummary } from "@/models/affiliate";
import { getUserApikeys } from "@/models/apikey";


async function getUserInfo() {
  const user_uuid = await getUserUuid();
  if (!user_uuid) {
    return respJson(-2, "no auth");
  }

  const user = await findUserByUuid(user_uuid);
  if (!user) {
    return respErr("user not exist");
  }

  // Get user credits info
  const userCredits = await getUserCredits(user_uuid);
  
  // Get additional data for modals
  const orders = await getOrdersByUserUuid(user_uuid);
  const credits = await getCreditsByUserUuid(user_uuid, 1, 100);
  const affiliates = await getUserAffiliates(user_uuid);
  const affiliateSummary = await getAffiliateSummary(user_uuid);
  const apiKeys = await getUserApikeys(user_uuid);

  const result = {
    ...user,
    credits: userCredits,
    userCredits,
    orders,
    creditsHistory: credits,
    affiliates,
    affiliateSummary,
    apiKeys
  };

  return respData(result);
}

export async function POST(req: Request) {
  try {
    return await getUserInfo();
  } catch (e) {
    console.log("get user info failed: ", e);
    return respErr("get user info failed");
  }
}

export async function GET() {
  try {
    return await getUserInfo();
  } catch (e) {
    console.log("get user info failed: ", e);
    return respErr("get user info failed");
  }
}
