import { NextResponse } from 'next/response';
import axios from 'axios';

export async function POST(req: Request) {
  try {
    const { subscriptionId } = await req.json();
    
    if (!subscriptionId) {
      return NextResponse.json(
        { success: false, error: 'Missing subscription ID' }, 
        { status: 400 }
      );
    }

    // 调用 Creem API 取消订阅
    const response = await axios.post(
      `https://api.creem.io/v1/subscriptions/${subscriptionId}/cancel`,
      {},
      {
        headers: { 
          "x-api-key": process.env.CREEM_API_KEY,
          "Content-Type": "application/json"
        }
      }
    );

    return NextResponse.json({ success: true, data: response.data });
  } catch (error: any) {
    console.error('Subscription cancellation failed:', error);
    return NextResponse.json(
      { success: false, error: error.response?.data?.message || error.message }, 
      { status: 500 }
    );
  }
}