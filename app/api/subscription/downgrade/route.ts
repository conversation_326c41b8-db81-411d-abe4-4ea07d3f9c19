import { NextResponse } from 'next/response';
import axios from 'axios';

export async function POST(req: Request) {
  try {
    const { subscriptionId, newProductId } = await req.json();
    
    if (!subscriptionId || !newProductId) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' }, 
        { status: 400 }
      );
    }

    // 调用 Creem API 降级订阅
    const response = await axios.post(
      `https://api.creem.io/v1/subscriptions/${subscriptionId}/upgrade`,
      {
        product_id: newProductId,
        update_behavior: "proration-none" // 下个周期生效，不退款
      },
      {
        headers: { 
          "x-api-key": process.env.CREEM_API_KEY,
          "Content-Type": "application/json"
        }
      }
    );

    return NextResponse.json({ success: true, data: response.data });
  } catch (error: any) {
    console.error('Subscription downgrade failed:', error);
    return NextResponse.json(
      { success: false, error: error.response?.data?.message || error.message }, 
      { status: 500 }
    );
  }
}