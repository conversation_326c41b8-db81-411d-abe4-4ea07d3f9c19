import { PaymentFactory } from '@/services/payment/factory';
import { respData } from '@/lib/resp';

export async function GET() {
  const enabledProviders = PaymentFactory.getEnabledProviders();
  const defaultProvider = PaymentFactory.getDefaultProvider();

  const providers = [
    {
      id: 'stripe',
      name: 'Stripe',
      description: '支持信用卡、微信支付、支付宝',
      logo: '/images/stripe-logo.png',
      enabled: enabledProviders.includes('stripe')
    },
    {
      id: 'creem',
      name: 'Creem',
      description: '新一代支付解决方案',
      logo: '/images/creem-logo.png',
      enabled: enabledProviders.includes('creem')
    }
  ];

  return respData({
    providers,
    defaultProvider,
    enabledProviders
  });
}
