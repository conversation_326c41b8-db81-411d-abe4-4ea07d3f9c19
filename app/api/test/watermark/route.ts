import { NextRequest, NextResponse } from 'next/server';
import { WatermarkService, WatermarkPosition } from '@/services/watermark';
import { newStorage } from '@/lib/storage';
import { getUuid } from '@/lib/hash';

export async function POST(req: NextRequest) {
  try {
    const { imageUrl, text, position, opacity, fontSize, fontSizeRatio, localOnly } = await req.json();

    if (!imageUrl) {
      return NextResponse.json({ error: 'Image URL is required' }, { status: 400 });
    }

    console.log(`[Watermark Test] Testing watermark on: ${imageUrl}`);

    // 配置水印参数
    const watermarkConfig = {
      text: text || 'kreaflux.org',
      position: position || WatermarkPosition.BOTTOM_RIGHT,
      opacity: opacity || 0.7,
      fontSize: fontSize, // 使用传入的字体大小，undefined 时自动计算
      fontSizeRatio: fontSizeRatio, // 字体大小比例
      color: '#FFFFFF',
      margin: 20
    };

    console.log(`[Watermark Test] Config:`, watermarkConfig);

    // 添加水印
    const watermarkedBuffer = await WatermarkService.addWatermarkFromUrl(imageUrl, watermarkConfig);

    console.log(`[Watermark Test] Watermark added, buffer size: ${watermarkedBuffer.length} bytes`);

    if (localOnly) {
      // 本地生成，返回base64数据
      const base64Data = watermarkedBuffer.toString('base64');
      const dataUrl = `data:image/png;base64,${base64Data}`;

      console.log(`[Watermark Test] Local generation completed`);

      return NextResponse.json({
        success: true,
        originalUrl: imageUrl,
        watermarkedUrl: dataUrl,
        config: watermarkConfig,
        fileSize: watermarkedBuffer.length,
        isLocal: true
      });
    } else {
      // 上传到存储
      const storage = newStorage();
      const taskId = getUuid();
      const key = `test/watermark/${taskId}.png`;
      const bucket = process.env.STORAGE_BUCKET || 'fluxkrea';

      const uploadResult = await storage.uploadFile({
        body: watermarkedBuffer,
        key: key,
        bucket: bucket,
        contentType: 'image/png',
        disposition: 'inline'
      });

      console.log(`[Watermark Test] Upload successful:`, uploadResult);

      return NextResponse.json({
        success: true,
        originalUrl: imageUrl,
        watermarkedUrl: uploadResult.url,
        config: watermarkConfig,
        fileSize: watermarkedBuffer.length,
        isLocal: false
      });
    }

  } catch (error) {
    console.error('[Watermark Test] Error:', error);
    return NextResponse.json(
      { 
        error: 'Watermark test failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }, 
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Watermark test endpoint',
    usage: 'POST with { imageUrl, text?, position?, opacity? }',
    example: {
      imageUrl: 'https://r2.kreaflux.org/fluxkrea/ai-generated/image/922fdc75-5bce-4173-907b-3bedf72623f7.png',
      text: 'kreaflux.org',
      position: 'bottom-right',
      opacity: 0.7
    }
  });
}
