'use client';

import { useState } from 'react';

export default function WatermarkTestPage() {
  const [imageUrl, setImageUrl] = useState('http://localhost:3000/imgs/showcases/flux-krea-showbox-2.png');
  const [text, setText] = useState('kreaflux.org');
  const [position, setPosition] = useState('bottom-right');
  const [opacity, setOpacity] = useState(0.7);
  const [fontSize, setFontSize] = useState<number | undefined>(undefined);
  const [fontSizeRatio, setFontSizeRatio] = useState<number>(5); // 默认5%
  const [localOnly, setLocalOnly] = useState(true);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testWatermark = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/test/watermark', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          text,
          position,
          opacity,
          fontSize,
          fontSizeRatio: fontSizeRatio / 100, // 转换为小数
          localOnly,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Test failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">水印服务测试</h1>

      <div className="space-y-6">
        {/* 配置表单 */}
        <div className=" p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">测试配置</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">图片 URL</label>
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="输入图片 URL"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">水印文字</label>
              <input
                type="text"
                value={text}
                onChange={(e) => setText(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="输入水印文字"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">水印位置</label>
              <select
                value={position}
                onChange={(e) => setPosition(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="top-left">左上角</option>
                <option value="top-right">右上角</option>
                <option value="bottom-left">左下角</option>
                <option value="bottom-right">右下角</option>
                <option value="center">居中</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">透明度: {opacity}</label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={opacity}
                onChange={(e) => setOpacity(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                字体大小: {fontSize ? `${fontSize}px` : '自动'}
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="16"
                  max="128"
                  step="1"
                  value={fontSize || 32}
                  onChange={(e) => setFontSize(parseInt(e.target.value))}
                  className="flex-1"
                />
                <button
                  type="button"
                  onClick={() => setFontSize(undefined)}
                  className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  自动
                </button>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                可选大小: 16px, 32px, 64px, 128px (自动选择最接近的)
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                自动字体大小比例: {fontSizeRatio}% (图片宽度)
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="1"
                  max="15"
                  step="0.5"
                  value={fontSizeRatio}
                  onChange={(e) => setFontSizeRatio(parseFloat(e.target.value))}
                  className="flex-1"
                />
                <button
                  type="button"
                  onClick={() => setFontSizeRatio(5)}
                  className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
                >
                  重置
                </button>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                当字体大小设为"自动"时，使用此比例计算字体大小
              </div>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={localOnly}
                  onChange={(e) => setLocalOnly(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm font-medium">本地生成 (不上传到服务器)</span>
              </label>
              <div className="text-xs text-gray-500 mt-1">
                勾选后将返回base64数据，不会上传到R2存储
              </div>
            </div>

            <button
              onClick={testWatermark}
              disabled={loading || !imageUrl}
              className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:bg-gray-400"
            >
              {loading ? '处理中...' : '测试水印'}
            </button>
          </div>
        </div>

        {/* 错误显示 */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>错误:</strong> {error}
          </div>
        )}

        {/* 结果显示 */}
        {result && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <h3 className="font-semibold mb-2">测试成功！</h3>
            <div className="space-y-2">
              <p><strong>原始图片:</strong> <a href={result.originalUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">查看</a></p>
              <p><strong>水印图片:</strong> <a href={result.watermarkedUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">查看</a></p>
              <p><strong>文件大小:</strong> {(result.fileSize / 1024).toFixed(2)} KB</p>
              <p><strong>生成方式:</strong> {result.isLocal ? '本地生成 (Base64)' : '上传到R2存储'}</p>
              {result.config && (
                <p><strong>实际字体大小:</strong> {result.config.fontSize || '自动计算'}</p>
              )}
            </div>
          </div>
        )}

        {/* 图片预览 */}
        {result && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">原始图片</h3>
              <img 
                src={result.originalUrl} 
                alt="原始图片" 
                className="w-full h-auto border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">添加水印后</h3>
              <img 
                src={result.watermarkedUrl} 
                alt="水印图片" 
                className="w-full h-auto border border-gray-300 rounded-md"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
