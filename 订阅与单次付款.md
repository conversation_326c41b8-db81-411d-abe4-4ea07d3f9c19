# Creem 订阅与单次付款处理方案

## 概述

基于 Creem 官方文档分析和实际代码实现，提供完整的单次付款和订阅续费处理解决方案。

**✅ 已实现功能**：
- 单次付款处理
- 首次订阅付款处理
- 订阅续费处理（含去重逻辑）
- 积分系统（区分单次和订阅积分有效期）
- Transaction ID 去重机制
- 推荐奖励处理（仅首次订阅）

**🎯 设计原则**：
- 避免过度设计，保持简单有效
- 明确区分首次订阅和续费逻辑
- 基于现有单表设计，最小化改动

## 1. 支付类型处理机制

### 单次付款
- **事件**：`checkout.completed`（不包含 subscription 对象）
- **状态**：当前已实现部分
- **处理**：检查增加订阅机制后是否有影响

### 订阅付款
- **首次付款**：`checkout.completed`（包含 subscription 对象）+ `subscription.active` + `subscription.paid`
- **续费付款**：`subscription.paid` ⭐ **关键事件**
- **重要**：
  - `subscription.active` 仅用于同步
  - **首次订阅会同时触发 `checkout.completed` 和 `subscription.paid`，需要去重处理**
  - **实际实现**：忽略首次订阅的 `checkout.completed`，用 `subscription.paid` 统一处理所有订阅付款

## 2. Webhook 事件处理完善

### 更新 CreemProvider.handleWebhook

```typescript
// services/payment/creem.ts
async handleWebhook(body: string, signature: string): Promise<WebhookResult> {
  try {
    const event = JSON.parse(body);
    console.log('[CREEM_WEBHOOK] Event type:', event.eventType);

    switch (event.eventType) {
      case 'checkout.completed':
        // 首次付款（单次付款或订阅首次）
        const hasSubscription = !!event.object.subscription;
        return {
          success: true,
          orderNo: event.object.metadata?.order_no,
          sessionId: event.object.id,
          eventType: 'checkout.completed',
          subscriptionData: hasSubscription ? event.object.subscription : null,
          isSubscription: hasSubscription
        };

      case 'subscription.paid':
        // 订阅付款（包括首次和续费，需要去重处理）
        return {
          success: true,
          subscriptionId: event.object.id,
          customerId: event.object.customer.id,
          eventType: 'subscription.paid',
          subscriptionData: event.object // 包含完整订阅信息
        };

      case 'subscription.active':
        // 仅用于同步，不处理业务逻辑
        return {
          success: true,
          eventType: 'subscription.active'
        };

      case 'subscription.canceled':
        return {
          success: true,
          subscriptionId: event.object.id,
          eventType: 'subscription.canceled'
        };

      case 'subscription.expired':
        return {
          success: true,
          subscriptionId: event.object.id,
          eventType: 'subscription.expired'
        };

      default:
        return { success: true };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}
```

### 更新 WebhookResult 类型

```typescript
// services/payment/types.ts
export interface WebhookResult {
  success: boolean;
  error?: string;
  orderNo?: string;
  sessionId?: string;
  eventType?: string;
  subscriptionId?: string;
  customerId?: string;
  subscriptionData?: any;
  isSubscription?: boolean; // 新增：标识是否为订阅
}
```

## 3. 数据库字段映射

### Orders 表字段与 Creem API 对应关系

| 数据库字段 | Creem API 字段 | 说明 | 数据类型 |
|-----------|---------------|------|----------|
| `sub_id` | `subscription.id` | 订阅ID | VARCHAR(255) |
| `sub_period_start` | `current_period_start_date` | 当前周期开始时间 | INT (时间戳) |
| `sub_period_end` | `current_period_end_date` | 当前周期结束时间 | INT (时间戳) |
| `sub_cycle_anchor` | `current_period_start_date` | 计费周期锚点 | INT (时间戳) |
| `sub_interval_count` | 从产品配置推导 | 计费间隔数量 | INT |
| `sub_times` | 自维护计数器 | 订阅付费次数 | INT |

### 数据库优化

```sql
-- 添加订阅查询索引
CREATE INDEX idx_orders_sub_id ON orders(sub_id);
CREATE INDEX idx_orders_user_subscription ON orders(user_uuid, sub_id);
CREATE INDEX idx_orders_subscription_status ON orders(sub_id, status);

-- 可选：添加订阅状态字段
ALTER TABLE orders ADD COLUMN sub_status VARCHAR(50);
-- 状态值：active, canceled, expired, trialing
```

## 4. 订阅处理逻辑实现

### ✅ 已实现：handlePaymentCallback

```typescript
// services/order.ts - 实际实现的代码
export async function handlePaymentCallback(
  provider: string,
  sessionId: string,
  orderNo?: string,
  eventType?: string,
  subscriptionData?: any,
  isSubscription?: boolean,
  transactionId?: string
) {
  try {
    if (eventType === 'checkout.completed') {
      if (isSubscription) {
        // 首次订阅的checkout.completed事件：忽略，等待subscription.paid事件处理
        console.log(`Ignoring checkout.completed for subscription, waiting for subscription.paid: ${orderNo}`);
        return;
      } else {
        // 处理单次付款
        await handleOneTimePayment(provider, sessionId, orderNo, transactionId);
      }
      return;
    }

    if (eventType === 'subscription.paid') {
      // 处理订阅付款（包括首次和续费，需要去重处理）
      await handleSubscriptionPayment(subscriptionData, transactionId);
      return;
    }

    // subscription.active 事件不需要处理业务逻辑
    if (eventType === 'subscription.active') {
      console.log('Subscription active event received for sync');
      return;
    }

    // 兼容旧版本调用（没有eventType的情况）
    await handleLegacyPaymentCallback(provider, sessionId, orderNo);
  } catch (error) {
    console.error(`Failed to process ${provider} payment callback:`, error);
    throw error;
  }
}

// 处理单次付款
async function handleOneTimePayment(
  provider: string,
  sessionId: string,
  orderNo: string
) {
  // 查找订单
  const order = await findOrderByOrderNo(orderNo);
  if (!order || order.status !== "created") {
    if (order?.status === "paid") {
      console.log(`Order already paid, skipping processing`);
      return;
    }
    throw new Error(`Order not found or invalid status`);
  }

  // 更新订单状态
  const paid_at = getIsoTimestr();
  const paid_detail = JSON.stringify({
    provider,
    sessionId,
    orderNo,
    processedVia: 'webhook',
    timestamp: paid_at
  });

  await updateOrderStatus(order.order_no, "paid", paid_at, order.user_email, paid_detail);

  // 发放积分：单次付款积分永不过期
  if (order.credits > 0) {
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: null, // 单次付款积分永不过期
      order_no: order.order_no,
    });
  }

  await updateAffiliateForOrder(order);
  console.log(`One-time payment processed: ${order.order_no}`);
}

// 处理首次订阅付款
async function handleFirstSubscriptionPayment(
  provider: string,
  sessionId: string,
  orderNo: string,
  subscriptionData: any
) {
  // 查找订单
  const order = await findOrderByOrderNo(orderNo);
  if (!order || order.status !== "created") {
    if (order?.status === "paid") {
      console.log(`Order already paid, skipping processing`);
      return;
    }
    throw new Error(`Order not found or invalid status`);
  }

  // 更新订单状态
  const paid_at = getIsoTimestr();
  const paid_detail = JSON.stringify({
    provider,
    sessionId,
    orderNo,
    subscriptionData,
    processedVia: 'webhook',
    timestamp: paid_at
  });

  await updateOrderStatus(order.order_no, "paid", paid_at, order.user_email, paid_detail);

  // 保存订阅信息
  const periodStart = new Date(subscriptionData.current_period_start_date).getTime() / 1000;
  const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;

  await updateOrderSubscription(
    order.order_no,
    subscriptionData.id,
    1, // sub_interval_count
    periodStart, // sub_cycle_anchor
    periodEnd, // sub_period_end
    periodStart, // sub_period_start
    'paid',
    paid_at,
    1, // sub_times (首次)
    order.user_email,
    JSON.stringify(subscriptionData)
  );

  // 发放积分：订阅积分在周期结束时过期
  if (order.credits > 0) {
    const expiredAt = new Date(periodEnd * 1000).toISOString();
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: expiredAt, // 订阅积分在当前周期结束时过期
      order_no: order.order_no,
    });
  }

  await updateAffiliateForOrder(order);
  console.log(`First subscription payment processed: ${order.order_no}`);
}
```

### ✅ 已实现：订阅付款处理函数（含去重逻辑）

```typescript
// services/order.ts - 实际实现的代码
async function handleSubscriptionPayment(subscriptionData: any, transactionId?: string) {
  try {
    const subscriptionId = subscriptionData.id;
    // 优先使用传入的transactionId，否则从subscriptionData中获取
    const finalTransactionId = transactionId || subscriptionData.last_transaction_id;

    // 1. 查找对应的订单
    // 首先尝试通过subscription ID查找（适用于续费）
    let order = await findOrderBySubscriptionId(subscriptionId);

    // 如果通过subscription ID找不到订单，尝试通过metadata中的order_no查找（适用于首次订阅）
    if (!order && subscriptionData.metadata?.order_no) {
      order = await findOrderByOrderNo(subscriptionData.metadata.order_no);
    }

    if (!order) {
      throw new Error(`Order not found for subscription: ${subscriptionId}, order_no: ${subscriptionData.metadata?.order_no || 'N/A'}`);
    }

    // 2. 检查是否已处理过此次交易（基于transaction ID去重）
    if (finalTransactionId) {
      const existingTransaction = await checkTransactionProcessed(order.order_no, finalTransactionId);
      if (existingTransaction) {
        console.log(`Transaction already processed: ${finalTransactionId}`);
        return;
      }
    }

    // 3. 判断是首次订阅还是续费
    const isFirstSubscription = order.status === 'created';
    const periodStart = new Date(subscriptionData.current_period_start_date).getTime() / 1000;
    const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;
    const currentSubTimes = isFirstSubscription ? 1 : (order.sub_times || 0) + 1;

    console.log(`Processing subscription payment: ${subscriptionId}, isFirst: ${isFirstSubscription}, times: ${currentSubTimes}`);

    // 4. 更新订阅信息
    await updateOrderSubscription(
      order.order_no,
      subscriptionId,
      1, // sub_interval_count
      periodStart, // sub_cycle_anchor
      periodEnd, // sub_period_end
      periodStart, // sub_period_start
      'paid', // status
      getIsoTimestr(), // paid_at
      currentSubTimes, // sub_times
      order.user_email, // paid_email
      JSON.stringify({ ...subscriptionData, transaction_id: finalTransactionId }) // paid_detail
    );

    // 5. 发放积分
    if (order.credits > 0) {
      const expiredAt = new Date(periodEnd * 1000).toISOString();
      if (isFirstSubscription) {
        // 首次订阅：直接发放积分
        await increaseCredits({
          user_uuid: order.user_uuid,
          trans_type: CreditsTransType.OrderPay,
          credits: order.credits,
          expired_at: expiredAt,
          order_no: order.order_no,
          transaction_id: finalTransactionId,
        });
      } else {
        // 续费：使用去重逻辑发放积分
        await handleSubscriptionCredits(order, periodEnd, finalTransactionId);
      }
    }

    // 6. 处理推荐奖励（仅首次订阅）
    if (isFirstSubscription) {
      await updateAffiliateForOrder(order);
    }

    console.log(`Subscription payment processed: ${subscriptionId}, isFirst: ${isFirstSubscription}, times: ${currentSubTimes}`);
  } catch (error) {
    console.error('Failed to handle subscription payment:', error);
    throw error;
  }
}

// 检查交易是否已处理
async function checkTransactionProcessed(orderNo: string, transactionId: string): Promise<boolean> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("paid_detail")
    .eq("order_no", orderNo)
    .single();

  if (error || !data) return false;

  try {
    const paidDetail = JSON.parse(data.paid_detail || '{}');
    return paidDetail.transaction_id === transactionId;
  } catch {
    return false;
  }
}
```

### 新增数据库查询函数

```typescript
// models/order.ts
export async function findOrderBySubscriptionId(
  sub_id: string
): Promise<Order | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("orders")
    .select("*")
    .eq("sub_id", sub_id)
    .eq("status", "paid")
    .single();

  if (error) {
    console.error('Error finding order by subscription ID:', error);
    return undefined;
  }

  return data;
}
```

## 5. 积分系统改进

### 订阅积分处理

```typescript
// services/credit.ts
async function handleSubscriptionCredits(order: Order, periodEnd: number, transactionId: string) {
  try {
    // 1. 计算新积分的过期时间（基于当前周期结束时间）
    const expiredAt = new Date(periodEnd * 1000).toISOString();

    // 2. 检查是否已经为此次交易发放过积分
    const existingCredit = await findCreditByTransactionId(order.order_no, transactionId);
    if (existingCredit) {
      console.log('Credits already granted for this transaction');
      return;
    }

    // 3. 发放新周期积分
    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: expiredAt, // 订阅积分在当前周期结束时过期
      order_no: order.order_no,
      transaction_id: transactionId, // 记录交易ID用于去重
    });

    console.log(`Subscription credits granted: ${order.credits} credits for user ${order.user_uuid}`);
  } catch (error) {
    console.error('Failed to handle subscription credits:', error);
    throw error;
  }
}

// 检查特定交易的积分是否已发放
async function findCreditByTransactionId(orderNo: string, transactionId: string): Promise<Credit | undefined> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("credits")
    .select("*")
    .eq("order_no", orderNo)
    .eq("transaction_id", transactionId)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }

  return data;
}
```

## 5.1 积分有效期说明

### 单次付款积分
- **有效期**：永不过期（`expired_at: null`）
- **原因**：用户一次性购买，积分应该持续有效
- **实现**：在 `handleOneTimePayment` 中设置 `expired_at: null`

### 订阅积分
- **有效期**：当前订阅周期结束时过期
- **原因**：订阅模式下，积分应该随订阅周期更新
- **实现**：基于 `current_period_end_date` 计算过期时间
- **周期更新**：每次续费时发放新积分，旧积分自动过期

```typescript
// 积分有效期计算示例
const periodEnd = new Date(subscriptionData.current_period_end_date).getTime() / 1000;
const expiredAt = new Date(periodEnd * 1000).toISOString();

// 单次付款
expired_at: null // 永不过期

// 订阅付款
expired_at: expiredAt // 周期结束时过期
```

## 6. Webhook 路由更新

```typescript
// app/api/payment-notify/[provider]/route.ts
export async function POST(req: Request, { params }: { params: Promise<{ provider: string }> }) {
  try {
    const { provider } = await params;
    const body = await req.text();
    const signature = req.headers.get('x-creem-signature') || '';

    const paymentProvider = PaymentFactory.createProvider(provider);
    const result = await paymentProvider.handleWebhook(body, signature);

    if (result.success) {
      switch (result.eventType) {
        case 'checkout.completed':
          if (result.orderNo && result.sessionId) {
            await handlePaymentCallback(
              provider,
              result.sessionId,
              result.orderNo,
              'checkout.completed',
              result.subscriptionData,
              result.isSubscription
            );
          }
          break;

        case 'subscription.paid':
          await handlePaymentCallback(
            provider,
            '',
            '',
            'subscription.paid',
            result.subscriptionData
          );
          break;

        case 'subscription.active':
          // 仅记录日志，不处理业务逻辑
          console.log('Subscription active event received for sync');
          break;

        case 'subscription.canceled':
        case 'subscription.expired':
          await handleSubscriptionStatusChange(result.subscriptionId, result.eventType);
          break;
      }
    }

    return respOk();
  } catch (error) {
    console.error(`Webhook failed:`, error);
    return respErr(`webhook failed: ${error.message}`);
  }
}

// 处理订阅状态变化
async function handleSubscriptionStatusChange(subscriptionId: string, eventType: string) {
  try {
    const order = await findOrderBySubscriptionId(subscriptionId);
    if (order) {
      // 更新订单状态或执行其他业务逻辑
      console.log(`Subscription ${subscriptionId} status changed to: ${eventType}`);
    }
  } catch (error) {
    console.error('Failed to handle subscription status change:', error);
  }
}
```

## 7. 实施步骤

### 立即实施
1. 更新 `CreemProvider.handleWebhook` 添加事件处理
2. 实现 `handleSubscriptionRenewal` 函数
3. 添加 `findOrderBySubscriptionId` 查询函数
4. 更新 webhook 路由处理逻辑

### 数据库优化
1. 添加订阅相关索引
2. 考虑添加 `sub_status` 字段

### 测试验证
1. 测试环境创建订阅产品
2. 模拟订阅续费流程
3. 验证积分发放和数据库更新

### 监控优化
1. 添加详细日志记录
2. 监控 webhook 处理成功率
3. 设置异常报警机制

## 8. 关键要点与最佳实践

### 🎯 设计决策说明

#### 为什么区分首次订阅和续费？
- **首次订阅**：需要处理推荐奖励、更新订单状态从 `created` → `paid`
- **续费**：只需要发放积分和更新订阅信息，避免重复处理推荐奖励
- **实现**：通过 `order.status === 'created'` 判断是否为首次订阅

#### 为什么续费时仍需要处理 order？
- 更新 `sub_times`（续费次数统计）
- 更新 `sub_period_end`（用于积分过期时间计算）
- 记录最新的付款信息（`paid_detail`, `paid_at`）
- 保持订阅状态同步

#### 为什么不使用单独的订阅表？
- **避免过度设计**：当前业务逻辑相对简单
- **保持简单**：单表设计减少复杂性
- **最小化改动**：基于现有架构进行扩展
- **未来扩展**：如需复杂订阅管理，可在此基础上重构

### 🔄 事件处理机制

#### 首次订阅事件序列
```
用户完成首次订阅付款
    ↓
checkout.completed (包含 subscription 对象)
    ↓
subscription.active (仅同步，不处理业务逻辑)
    ↓
subscription.paid (需要去重，避免重复处理)
```

#### 续费事件序列
```
订阅自动续费
    ↓
subscription.paid (处理续费逻辑)
```

#### 去重策略
- **首次订阅**：忽略 `checkout.completed` 事件，只处理 `subscription.paid` 事件
- **续费**：只处理 `subscription.paid` 事件
- **Transaction ID**：用于防止重复处理同一笔交易

### 💰 积分管理策略

#### 单次付款积分
```typescript
expired_at: null // 永不过期
```
- **原因**：用户一次性购买，积分应该持续有效
- **适用场景**：买断制产品、一次性服务

#### 订阅积分
```typescript
expired_at: new Date(periodEnd * 1000).toISOString() // 周期结束时过期
```
- **原因**：订阅模式下，积分应该随订阅周期更新
- **适用场景**：月度/年度订阅服务
- **周期更新**：每次续费发放新积分，旧积分自动过期

### 🔍 查找订单逻辑

#### 双重查找机制
```typescript
// 1. 首先通过 subscription ID 查找（适用于续费）
let order = await findOrderBySubscriptionId(subscriptionId);

// 2. 如果找不到，通过 metadata 中的 order_no 查找（适用于首次订阅）
if (!order && subscriptionData.metadata?.order_no) {
  order = await findOrderByOrderNo(subscriptionData.metadata.order_no);
}
```

#### 为什么需要双重查找？
- **续费场景**：订单已有 `sub_id`，可直接通过 subscription ID 查找
- **首次订阅**：订单还没有 `sub_id`，需要通过 metadata 中的 `order_no` 查找
- **Creem 机制**：checkout session 的 metadata 会保存到 subscription 对象中

### 🛡️ 去重保护机制

#### Transaction ID 去重
```typescript
// 检查是否已处理过此次交易
if (finalTransactionId) {
  const existingTransaction = await checkTransactionProcessed(order.order_no, finalTransactionId);
  if (existingTransaction) {
    console.log(`Transaction already processed: ${finalTransactionId}`);
    return;
  }
}
```

#### 为什么需要去重？
- **首次订阅**：虽然忽略了 `checkout.completed` 事件，但仍需防止 `subscription.paid` 事件重复处理
- **网络重试**：webhook 可能因网络问题重复发送
- **数据一致性**：防止重复发放积分或处理推荐奖励
- **Transaction ID 唯一性**：确保每个交易只被处理一次

### 📊 数据库设计考虑

#### 当前单表设计的优缺点

**✅ 优点**：
- 简单直观，易于理解和维护
- 查询性能好，无需复杂的 JOIN 操作
- 适合当前的业务规模

**⚠️ 限制**：
- 无法详细追踪每次续费的具体信息
- 退款处理相对复杂
- 财务审计功能有限

#### 何时考虑重构？
- 需要详细的财务记录和审计功能
- 需要复杂的退款逻辑
- 需要支持多种订阅计划和升级/降级
- 业务规模增长到需要更精细的数据管理

### 🔧 函数职责分离

#### 清晰的函数边界
- **`handleOneTimePayment`**：专门处理单次付款，积分永不过期
- **`handleSubscriptionPayment`**：统一处理订阅付款，自动区分首次和续费
- **`handleSubscriptionCredits`**：专门处理订阅积分的去重发放
- **`checkTransactionProcessed`**：通用的交易去重检查

#### 为什么这样设计？
- **单一职责**：每个函数只负责一种场景
- **易于测试**：功能边界清晰，便于单元测试
- **易于维护**：修改某种场景的逻辑不会影响其他场景
- **代码复用**：通用逻辑可以被多个函数复用

### 📈 监控和日志

#### 关键日志点
```typescript
console.log(`Processing subscription payment: ${subscriptionId}, isFirst: ${isFirstSubscription}, times: ${currentSubTimes}`);
console.log(`Transaction already processed: ${finalTransactionId}`);
console.log(`Subscription payment processed: ${subscriptionId}, isFirst: ${isFirstSubscription}, times: ${currentSubTimes}`);
```

#### 建议监控指标
- Webhook 处理成功率
- 订阅续费成功率
- 积分发放准确性
- 去重机制有效性
- 推荐奖励处理准确性

### 🚀 未来扩展考虑

#### 如果需要更复杂的订阅管理
1. **添加订阅表**：专门存储订阅状态和历史
2. **订单表改为交易记录**：每次付款创建新的订单记录
3. **积分表增强**：支持更复杂的积分规则和历史追踪
4. **事件表**：记录所有 webhook 事件，便于调试和审计

#### 当前设计的扩展性
- 数据库字段已预留订阅相关字段
- 代码结构支持新增处理逻辑
- 去重机制可扩展到更复杂场景
- 积分系统可支持更多规则
